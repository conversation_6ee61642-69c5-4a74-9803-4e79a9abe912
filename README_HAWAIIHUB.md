# 🏝️ HawaiiHub 夏威夷华人门户系统

[![火鸟CMS](https://img.shields.io/badge/火鸟CMS-v8.6-blue.svg)](https://www.kumanyun.com/)
[![PHP](https://img.shields.io/badge/PHP-7.4+-green.svg)](https://php.net/)
[![License](https://img.shields.io/badge/License-Commercial-red.svg)](LICENSE)

## 📋 项目简介

HawaiiHub是基于火鸟门户系统(HuoNiaoCMS v8.6)开发的夏威夷本地华人社区门户网站，致力于为夏威夷华人提供全方位的信息服务和社区交流平台。

## 🚀 主要功能

### 🏢 核心模块
- **新闻资讯** - 夏威夷本地新闻、华人社区动态
- **分类信息** - 房屋租售、二手交易、生活服务
- **招聘求职** - 本地就业机会、人才招聘
- **商家黄页** - 华人商家目录、服务推荐
- **社区论坛** - 华人交流、经验分享
- **活动发布** - 社区活动、聚会组织

### 🤖 AI集成功能
- **智能内容采集** - 自动化新闻和信息收集
- **多语言支持** - 中英文双语服务
- **智能推荐** - 个性化内容推送
- **聊天机器人** - 24/7客户服务支持

## 🛠️ 技术架构

### 系统环境
- **操作系统**: Linux 6.8.0-49-generic
- **Web服务器**: Nginx 1.24.0
- **PHP版本**: 7.4.33
- **数据库**: MySQL 5.7.44-log
- **管理面板**: 宝塔面板

### 核心技术
- **后端框架**: 火鸟CMS v8.6 Release utf-8
- **前端技术**: HTML5, CSS3, JavaScript, jQuery
- **模板引擎**: Smarty
- **数据库**: MySQL + Redis缓存
- **API接口**: RESTful API

## 📁 项目结构

```
hawaiihub.net/
├── admin/              # 后台管理系统
├── api/                # API接口
├── docs/               # 项目文档
├── include/            # 核心类库
├── static/             # 静态资源
├── templates/          # 模板文件
├── scripts/            # 自动化脚本
├── hawaiihub-ai/       # AI集成模块
├── nextchat/           # ChatGPT集成
├── design/             # 设计组件
└── 插件/               # 系统插件
```

## 🔧 安装部署

### 环境要求
- PHP 7.4+
- MySQL 5.7+
- Nginx/Apache
- Redis (可选)

### 快速开始
```bash
# 克隆项目
git clone [repository-url] hawaiihub.net
cd hawaiihub.net

# 安装依赖
composer install

# 配置数据库
cp config.sample.php config.php
# 编辑config.php配置数据库连接

# 设置权限
chmod -R 755 ./
chmod -R 777 data/ uploads/ templates_c/

# 访问安装向导
http://your-domain.com/install/
```

## 📚 文档说明

- [系统管理文档](docs/01_系统管理/)
- [API接口文档](docs/02_API接口/)
- [功能模块文档](docs/03_功能模块/)
- [开发文档](docs/04_开发文档/)
- [用户指南](docs/05_用户指南/)
- [采集插件文档](docs/06_采集插件/)

## 🔒 安全规范

### 生产环境安全
- 定期更新系统和依赖包
- 使用HTTPS加密传输
- 配置防火墙和访问控制
- 定期备份数据库和文件
- 监控系统日志和异常

### 开发规范
- 遵循PSR编码标准
- 使用Git版本控制
- 代码审查和测试
- 文档同步更新

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目基于火鸟CMS商业许可证，仅供HawaiiHub项目使用。

## 📞 联系我们

- **网站**: https://hawaiihub.net
- **邮箱**: <EMAIL>
- **技术支持**: 通过GitHub Issues

## 🙏 致谢

- [火鸟CMS](https://www.kumanyun.com/) - 提供强大的CMS基础框架
- [宝塔面板](https://www.bt.cn/) - 提供便捷的服务器管理
- 夏威夷华人社区 - 提供宝贵的需求反馈和支持

---

**© 2025 HawaiiHub. All rights reserved.**
