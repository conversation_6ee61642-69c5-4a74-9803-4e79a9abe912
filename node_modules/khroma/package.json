{"name": "khroma", "repository": "github:fabios<PERSON><PERSON><PERSON><PERSON>/khroma", "description": "A collection of functions for manipulating CSS colors, inspired by SASS.", "version": "2.0.0", "type": "module", "sideEffects": false, "main": "dist/index.js", "exports": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"benchmark": "tsex benchmark", "benchmark:watch": "tsex benchmark --watch", "clean": "tsex clean", "compile": "tsex compile", "compile:watch": "tsex compile --watch", "test": "tsex test", "test:watch": "tsex test --watch", "prepublishOnly": "npm run clean && npm run compile && npm run test"}, "keywords": ["sass", "color", "manipulation", "manipulate", "css", "hex", "rgb", "hsl"], "devDependencies": {"benchloop": "^1.3.2", "fava": "^0.0.6", "tsex": "^1.1.0", "typescript": "^4.6.3"}}