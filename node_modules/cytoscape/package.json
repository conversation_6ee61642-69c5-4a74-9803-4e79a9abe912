{"name": "cytoscape", "version": "3.24.0", "license": "MIT", "description": "Graph theory (a.k.a. network) library for analysis and visualisation", "homepage": "http://js.cytoscape.org", "repository": {"type": "git", "url": "https://github.com/cytoscape/cytoscape.js.git"}, "bugs": {"url": "https://github.com/cytoscape/cytoscape.js/issues"}, "keywords": ["graph", "graph-theory", "network", "node", "edge", "vertex", "link", "analysis", "visualisation", "visualization", "draw", "render", "biojs", "cytoscape"], "engines": {"node": ">=0.10"}, "main": "dist/cytoscape.cjs.js", "unpkg": "dist/cytoscape.min.js", "jsdelivr": "dist/cytoscape.min.js", "scripts": {"lint": "eslint src benchmark", "build": "rollup -c", "build:esm": "cross-env FILE=esm rollup -c", "build:esm.min": "cross-env FILE=esm.min rollup -c", "build:cjs": "cross-env FILE=cjs rollup -c", "build:umd": "cross-env FILE=umd rollup -c", "build:min": "cross-env FILE=min rollup -c", "clean": "rimraf build/*", "copyright": "node -r esm license-update", "dist:copy": "cpy build/cytoscape.umd.js build/cytoscape.min.js build/cytoscape.cjs.js build/cytoscape.esm.js build/cytoscape.esm.min.js dist", "dist": "cross-env NODE_ENV=production run-s build dist:*", "release": "run-s copyright dist docs", "postpublish": "run-s docs:push", "watch": "run-s watch:fast", "watch:sync": "livereload \"build, debug\" -w 500", "watch:http": "http-server -s -c -1 -o debug", "watch:fast": "run-p watch:sync watch:http watch:build:fast", "watch:umd": "run-p watch:sync watch:http watch:build:umd", "watch:build:fast": "cross-env FILE=umd SOURCEMAPS=true BABEL=false NODE_ENV=development rollup -c -w", "watch:build:umd": "cross-env FILE=umd SOURCEMAPS=true NODE_ENV=development rollup -c -w", "watch:build:cjs": "cross-env FILE=cjs SOURCEMAPS=true NODE_ENV=development rollup -c -w", "test": "run-s test:js test:modules lint", "test:js": "mocha -r esm --recursive", "test:js:debug": "mocha inspect -r esm --recursive", "test:build": "cross-env TEST_BUILD=true mocha", "test:modules": "mocha -r esm test/modules", "test:modules:debug": "mocha inspect -r esm test/modules", "travis": "run-s build test:build test:modules lint", "docs": "run-s docs:build docs:js", "docs:js": "cpy build/cytoscape.min.js documentation/js", "docs:build": "node documentation/docmaker.js", "docs:push": "gh-pages -d documentation", "benchmark": "run-s benchmark:all", "benchmark:download": "download https://raw.githubusercontent.com/cytoscape/cytoscape.js/master/dist/cytoscape.cjs.js --out build --filename cytoscape.benchmark.js", "benchmark:all:exec": "node benchmark/all", "benchmark:all": "run-s benchmark:download benchmark:all:exec", "benchmark:single:exec": "node benchmark/single", "benchmark:single": "run-s benchmark:download benchmark:single:exec"}, "devDependencies": {"@babel/core": "^7.3.4", "@babel/preset-env": "^7.5.5", "@rollup/plugin-babel": "^5.0.0", "@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-node-resolve": "^7.1.3", "@rollup/plugin-replace": "^2.3.2", "benchmark": "^2.1.4", "bluebird": "^3.5.0", "chai": "^4.1.2", "cpy-cli": "^3.1.1", "cross-env": "^7.0.0", "download-cli": "^1.0.5", "eslint": "^6.0.0", "esm": "^3.2.25", "gh-pages": "^2.1.1", "handlebars": "^4.7.6", "highlight.js": "^10.0.0", "http-server": "^0.12.3", "jsonlint": "^1.6.2", "livereload": "^0.9.1", "marked": "^4.0.10", "mocha": "^7.1.2", "npm-run-all": "^4.1.5", "rimraf": "^3.0.0", "rollup": "^2.8.2", "rollup-plugin-license": "^2.3.0", "rollup-plugin-terser": "^5.3.0"}, "dependencies": {"heap": "^0.2.6", "lodash": "^4.17.21"}}