---
alwaysApply: true
type: "always_apply"
---
# HawaiiHub火鸟门户系统通用开发规则

## 🎯 项目概述
HawaiiHub火鸟门户系统是一个基于PHP的企业级门户网站系统，采用模块化架构，包含多个插件和功能模块。

## 📋 核心开发原则

### ✅ 必须遵循的原则：
1. **基于官方源码分析** - 所有结论必须来自真实代码文件
2. **不得猜想/设想** - 不能基于经验或推测给出答案
3. **找不到答案时直接汇报** - 明确告知"暂无结果"
4. **代码质量优先** - 确保代码的可读性、可维护性和安全性
5. **文档驱动开发** - 重要功能必须有相应的文档说明

### ❌ 严格禁止事项：
- **修改官方源码文件** - 不得直接修改系统核心文件
- **推测未实现功能** - 不能假设系统有某些功能
- **基于文档推测** - 不能仅凭文档推测实际功能
- **提供通用解决方案** - 必须基于火鸟系统具体实现
- **代码修改建议** - 不得提供修改官方源码的建议
- **功能扩展设计** - 不得设计系统未实现的功能

## 🔧 技术栈规范

### PHP开发规范
```php
// 文件编码：UTF-8
// 缩进：4个空格
// 命名规范：驼峰命名法
// 注释：中文注释，详细说明功能

/**
 * 功能说明
 * @param string $param 参数说明
 * @return array 返回值说明
 */
function exampleFunction($param) {
    // 实现逻辑
    return $result;
}
```

### 数据库操作规范
```php
// 使用预处理语句防止SQL注入
$sql = "SELECT * FROM users WHERE id = ?";
$result = $db->getRow($sql, array($userid));

// 事务处理
$db->beginTransaction();
try {
    // 数据库操作
    $db->commit();
} catch (Exception $e) {
    $db->rollback();
    throw $e;
}
```

### 前端开发规范
```html
<!-- HTML结构清晰，语义化标签 -->
<div class="container">
    <header class="page-header">
        <h1>页面标题</h1>
    </header>
    <main class="page-content">
        <!-- 内容区域 -->
    </main>
</div>
```

```css
/* CSS采用BEM命名规范 */
.container {
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    padding: 20px 0;
    background: #f5f5f5;
}

.page-header__title {
    font-size: 24px;
    color: #333;
}
```

```javascript
// JavaScript采用ES6+语法
const config = {
    apiUrl: '/api',
    timeout: 5000
};

class ApiService {
    constructor() {
        this.baseUrl = config.apiUrl;
    }
    
    async request(endpoint, options = {}) {
        try {
            const response = await fetch(`${this.baseUrl}${endpoint}`, {
                ...options,
                timeout: config.timeout
            });
            return await response.json();
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }
}
```

## 📁 项目结构规范

### 目录组织
```
/
├── admin/              # 后台管理
├── api/                # API接口
├── 插件/               # 插件模块
├── docs/               # 文档
├── static/             # 静态资源
├── templates/          # 模板文件
├── vendor/             # 第三方依赖
└── .cursor/            # Cursor配置
    └── rules/          # 规则文件
```

### 文件命名规范
- **PHP文件**: 小写字母，下划线分隔，如 `user_service.php`
- **模板文件**: 小写字母，下划线分隔，如 `user_list.html`
- **CSS文件**: 小写字母，连字符分隔，如 `user-profile.css`
- **JS文件**: 小写字母，连字符分隔，如 `user-profile.js`

## 🔒 安全规范

### 输入验证
```php
// 所有用户输入都需要验证
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$title = isset($_POST['title']) ? trim($_POST['title']) : '';

// 防止XSS攻击
$content = htmlspecialchars($content, ENT_QUOTES, 'UTF-8');

// 防止CSRF攻击
if (!isset($_POST['token']) || $_POST['token'] !== $_SESSION['csrf_token']) {
    die('CSRF验证失败');
}
```

### 权限验证
```php
// 检查用户登录状态
if (!$userLogin->isLoggedIn()) {
    header("Location: /login.php");
    exit;
}

// 检查用户权限
if ($userinfo['level'] < 2) {
    die('权限不足');
}
```

## 📝 文档规范

### 代码注释
```php
/**
 * 用户服务类
 * 处理用户相关的业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-28
 */
class UserService {
    /**
     * 创建新用户
     * 
     * @param array $userData 用户数据
     * @return int|false 成功返回用户ID，失败返回false
     * @throws Exception 当数据验证失败时抛出异常
     */
    public function createUser($userData) {
        // 实现逻辑
    }
}
```

### 文档结构
```markdown
# 功能名称

## 功能描述
详细说明功能的作用和用途

## 使用方法
提供具体的使用示例

## 参数说明
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| param1 | string | 是 | 参数说明 |

## 返回值
说明返回值的格式和含义

## 注意事项
列出使用时需要注意的事项
```

## 🚀 性能优化规范

### 数据库优化
```php
// 使用索引优化查询
$sql = "SELECT * FROM users WHERE status = ? AND created_at > ?";
$users = $db->getAll($sql, array($status, $date));

// 分页查询
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$limit = 20;
$offset = ($page - 1) * $limit;
$sql = "SELECT * FROM articles LIMIT ? OFFSET ?";
```

### 缓存策略
```php
// 使用缓存减少数据库查询
$cacheKey = "user_profile_{$userid}";
$userData = $cache->get($cacheKey);

if (!$userData) {
    $userData = $db->getRow("SELECT * FROM users WHERE id = ?", array($userid));
    $cache->set($cacheKey, $userData, 3600); // 缓存1小时
}
```

## 🧪 测试规范

### 单元测试
```php
/**
 * 用户服务测试类
 */
class UserServiceTest extends PHPUnit_Framework_TestCase {
    
    public function testCreateUser() {
        $userService = new UserService();
        $userData = [
            'username' => 'testuser',
            'email' => '<EMAIL>'
        ];
        
        $result = $userService->createUser($userData);
        $this->assertIsInt($result);
    }
}
```

## 📊 日志规范

### 日志记录
```php
// 记录重要操作日志
function logUserAction($userid, $action, $details = '') {
    $logData = [
        'userid' => $userid,
        'action' => $action,
        'details' => $details,
        'ip' => $_SERVER['REMOTE_ADDR'],
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    $db->insert('user_logs', $logData);
}
```

## 🔄 版本控制规范

### Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 分支管理
- `main`: 主分支，稳定版本
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 紧急修复分支

## 📞 协作规范

### 代码审查
- 所有代码提交前必须经过审查
- 重点关注安全性和性能问题
- 确保代码符合项目规范

### 问题反馈
- 使用GitHub Issues或内部工单系统
- 提供详细的问题描述和复现步骤
- 及时响应和解决反馈问题

---

**基于火鸟门户系统实际需求制定，最后