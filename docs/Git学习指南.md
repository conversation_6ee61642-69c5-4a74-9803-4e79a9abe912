# Git学习指南 - HawaiiHub项目专用

## 📋 目录
1. [Git基础概念](#git基础概念)
2. [Git安装与配置](#git安装与配置)
3. [常用Git命令](#常用git命令)
4. [解决当前问题](#解决当前问题)
5. [Git最佳实践](#git最佳实践)
6. [常见问题与解决方案](#常见问题与解决方案)

---

## 🎯 Git基础概念

### 什么是Git？
Git是一个分布式版本控制系统，用于跟踪文件的变化。它可以帮助您：
- 保存代码的历史版本
- 多人协作开发
- 管理不同的功能分支
- 回滚到之前的版本

### Git的核心概念

#### 1. 工作区（Working Directory）
- 您实际编辑文件的地方
- 项目根目录就是工作区

#### 2. 暂存区（Staging Area）
- 准备提交的文件临时存放区域
- 使用 `git add` 将文件添加到暂存区

#### 3. 仓库（Repository）
- 存储所有版本信息的地方
- 包含完整的项目历史

#### 4. 分支（Branch）
- 独立的工作线
- 默认分支通常是 `main` 或 `master`

---

## ⚙️ Git安装与配置

### 安装Git

#### macOS安装
```bash
# 使用Homebrew安装
brew install git

# 或下载官方安装包
# https://git-scm.com/download/mac
```

#### 验证安装
```bash
git --version
```

### 初始配置

#### 设置用户信息
```bash
# 设置用户名和邮箱
git config --global user.name "您的姓名"
git config --global user.email "您的邮箱@example.com"

# 查看配置
git config --list
```

#### 设置默认编辑器
```bash
# 设置VS Code为默认编辑器
git config --global core.editor "code --wait"

# 或设置vim
git config --global core.editor "vim"
```

---

## 🛠️ 常用Git命令

### 初始化仓库

#### 创建新仓库
```bash
# 在项目目录中初始化Git仓库
git init

# 查看仓库状态
git status
```

#### 克隆现有仓库
```bash
# 克隆远程仓库
git clone https://github.com/用户名/仓库名.git

# 克隆到指定目录
git clone https://github.com/用户名/仓库名.git 目录名
```

### 基本操作流程

#### 1. 查看状态
```bash
# 查看当前状态
git status

# 查看简短状态
git status --short
```

#### 2. 添加文件到暂存区
```bash
# 添加特定文件
git add 文件名.php

# 添加所有文件
git add .

# 添加特定类型文件
git add *.php

# 交互式添加
git add -i
```

#### 3. 提交更改
```bash
# 提交暂存区的更改
git commit -m "提交说明"

# 添加并提交（跳过暂存区）
git add . && git commit -m "提交说明"

# 修改最后一次提交
git commit --amend
```

#### 4. 查看历史
```bash
# 查看提交历史
git log

# 查看简洁历史
git log --oneline

# 查看图形化历史
git log --graph --oneline --all
```

### 分支操作

#### 创建和切换分支
```bash
# 创建新分支
git branch 分支名

# 创建并切换到新分支
git checkout -b 分支名

# 使用新命令创建并切换分支
git switch -c 分支名

# 切换到现有分支
git checkout 分支名
# 或
git switch 分支名
```

#### 合并分支
```bash
# 合并分支到当前分支
git merge 分支名

# 使用rebase合并
git rebase 分支名
```

#### 删除分支
```bash
# 删除本地分支
git branch -d 分支名

# 强制删除分支
git branch -D 分支名
```

### 远程仓库操作

#### 添加远程仓库
```bash
# 添加远程仓库
git remote add origin https://github.com/用户名/仓库名.git

# 查看远程仓库
git remote -v
```

#### 推送和拉取
```bash
# 推送到远程仓库
git push origin 分支名

# 拉取远程更改
git pull origin 分支名

# 获取远程信息（不合并）
git fetch origin
```

---

## 🔧 解决当前问题

### 问题分析
根据图片显示的问题："Git仓库中存在太多活动更改，将仅启用部分Git功能"

### 解决方案

#### 1. 检查当前状态
```bash
# 查看所有更改
git status

# 查看详细更改
git diff

# 查看暂存区的更改
git diff --cached
```

#### 2. 清理不必要的文件

##### 创建.gitignore文件
```bash
# 在项目根目录创建.gitignore
touch .gitignore
```

##### 添加常见忽略规则
```gitignore
# 系统文件
.DS_Store
Thumbs.db

# 编辑器文件
.vscode/
.idea/
*.swp
*.swo

# 临时文件
*.tmp
*.temp
*.log

# 依赖目录
node_modules/
vendor/
venv/

# 构建文件
dist/
build/
*.min.js
*.min.css

# 上传文件
uploads/
static/uploads/

# 缓存文件
cache/
templates_c/
```

#### 3. 移除已跟踪的不必要文件
```bash
# 从Git中移除文件（保留本地文件）
git rm --cached 文件名

# 移除目录
git rm -r --cached 目录名

# 批量移除
git rm --cached -r .
git add .
git commit -m "清理不必要的文件"
```

#### 4. 分批提交更改
```bash
# 按文件类型分批提交
git add *.php
git commit -m "更新PHP文件"

git add *.js
git commit -m "更新JavaScript文件"

git add *.css
git commit -m "更新CSS文件"
```

#### 5. 使用Git LFS处理大文件
```bash
# 安装Git LFS
brew install git-lfs

# 初始化LFS
git lfs install

# 跟踪大文件
git lfs track "*.zip"
git lfs track "*.pdf"
git lfs track "*.mp4"

# 提交LFS配置
git add .gitattributes
git commit -m "配置Git LFS"
```

---

## 🎯 Git最佳实践

### 提交信息规范

#### 提交信息格式
```
类型(范围): 简短描述

详细描述（可选）

相关链接（可选）
```

#### 类型说明
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具变动

#### 示例
```bash
git commit -m "feat(admin): 添加用户管理功能

- 新增用户列表页面
- 实现用户增删改查功能
- 添加权限控制

Closes #123"
```

### 分支管理策略

#### 主分支
- `main`: 生产环境代码
- `develop`: 开发环境代码

#### 功能分支
- `feature/功能名`: 新功能开发
- `hotfix/问题描述`: 紧急修复
- `release/版本号`: 发布准备

#### 分支命名规范
```bash
# 功能分支
git checkout -b feature/user-management

# 修复分支
git checkout -b hotfix/login-bug

# 发布分支
git checkout -b release/v1.2.0
```

### 工作流程

#### 1. 开始新功能
```bash
# 确保主分支是最新的
git checkout main
git pull origin main

# 创建功能分支
git checkout -b feature/新功能名

# 开始开发...
```

#### 2. 提交更改
```bash
# 添加更改
git add .

# 提交更改
git commit -m "feat: 添加新功能"

# 推送到远程
git push origin feature/新功能名
```

#### 3. 合并功能
```bash
# 切换到主分支
git checkout main

# 合并功能分支
git merge feature/新功能名

# 删除功能分支
git branch -d feature/新功能名
git push origin --delete feature/新功能名
```

---

## ❓ 常见问题与解决方案

### 1. 撤销更改

#### 撤销工作区的更改
```bash
# 撤销特定文件
git checkout -- 文件名

# 撤销所有更改
git checkout -- .

# 使用新命令
git restore 文件名
```

#### 撤销暂存区的更改
```bash
# 撤销暂存区的更改
git reset HEAD 文件名

# 使用新命令
git restore --staged 文件名
```

#### 撤销提交
```bash
# 撤销最后一次提交（保留更改）
git reset --soft HEAD~1

# 撤销最后一次提交（删除更改）
git reset --hard HEAD~1

# 创建新的提交来撤销
git revert HEAD
```

### 2. 解决冲突

#### 合并冲突
```bash
# 当合并出现冲突时
git status  # 查看冲突文件

# 手动编辑冲突文件，然后
git add 冲突文件
git commit -m "解决合并冲突"
```

#### 冲突标记
```
<<<<<<< HEAD
当前分支的代码
=======
要合并分支的代码
>>>>>>> 分支名
```

### 3. 查看历史

#### 查看提交历史
```bash
# 查看详细历史
git log

# 查看简洁历史
git log --oneline

# 查看图形化历史
git log --graph --oneline --all

# 查看特定文件的历史
git log --follow 文件名
```

#### 查看更改
```bash
# 查看工作区的更改
git diff

# 查看暂存区的更改
git diff --cached

# 查看特定提交的更改
git show 提交ID
```

### 4. 标签管理

#### 创建标签
```bash
# 创建轻量标签
git tag v1.0.0

# 创建带注释的标签
git tag -a v1.0.0 -m "版本1.0.0发布"

# 推送标签
git push origin v1.0.0

# 推送所有标签
git push origin --tags
```

#### 查看标签
```bash
# 查看所有标签
git tag

# 查看标签详情
git show v1.0.0
```

---

## 📚 学习资源

### 官方文档
- [Git官方文档](https://git-scm.com/doc)
- [Git教程](https://git-scm.com/book/zh/v2)

### 在线学习
- [GitHub Learning Lab](https://lab.github.com/)
- [Git教程 - 廖雪峰](https://www.liaoxuefeng.com/wiki/896043488029600)

### 图形化工具
- [GitHub Desktop](https://desktop.github.com/)
- [SourceTree](https://www.sourcetreeapp.com/)
- [GitKraken](https://www.gitkraken.com/)

---

## 🎉 总结

通过本指南，您应该能够：

1. ✅ 理解Git的基本概念
2. ✅ 掌握常用Git命令
3. ✅ 解决当前仓库问题
4. ✅ 遵循Git最佳实践
5. ✅ 处理常见Git问题

### 下一步建议

1. **实践练习**: 在测试项目中练习Git命令
2. **创建.gitignore**: 为您的项目创建合适的忽略规则
3. **分批提交**: 将大量更改分批提交
4. **学习高级功能**: 深入了解Git的高级功能
5. **团队协作**: 学习多人协作的Git工作流程

记住：Git是一个强大的工具，需要时间和实践来掌握。不要害怕犯错，Git提供了很多撤销操作的方法！

---

*最后更新: 2025年1月28日* 