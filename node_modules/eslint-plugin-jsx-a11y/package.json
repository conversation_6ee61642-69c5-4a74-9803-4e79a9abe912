{"name": "eslint-plugin-jsx-a11y", "version": "6.7.1", "description": "Static AST checker for accessibility rules on JSX elements.", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "a11y", "accessibility", "jsx"], "author": "<PERSON>", "repository": {"type": "git", "url": "https://github.com/jsx-eslint/eslint-plugin-jsx-a11y"}, "main": "lib/index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated && npm run build", "build": "rimraf lib && babel src --out-dir lib --copy-files", "create": "node ./scripts/create-rule", "flow": "flow", "lint:fix": "npm run lint -- --fix", "lint": "eslint --ext=js,mjs,cjs,ts,tsx .", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest && npm run lint && npm run flow && npm run jest", "pretest": "npm run lint:fix && npm run flow", "test": "npm run jest", "posttest": "aud --production", "test:ci": "npm run jest -- --ci --runInBand", "jest": "jest --coverage __tests__/**/*", "pregenerate-list-of-rules": "npm run build", "generate-list-of-rules": "eslint-doc-generator --rule-doc-title-format prefix-name --rule-doc-section-options false --config-emoji recommended,☑️", "generate-list-of-rules:check": "npm run generate-list-of-rules -- --check", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "devDependencies": {"@babel/cli": "^7.20.7", "@babel/core": "^7.20.12", "@babel/eslint-parser": "^7.19.1", "@babel/plugin-transform-flow-strip-types": "^7.19.0", "@babel/register": "^7.18.9", "ast-types-flow": "^0.0.7", "aud": "^2.0.2", "auto-changelog": "^2.4.0", "babel-jest": "^24.9.0", "babel-plugin-add-module-exports": "^1.0.4", "babel-preset-airbnb": "^5.0.0", "eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8", "eslint-config-airbnb-base": "^15.0.0", "eslint-doc-generator": "^1.4.1", "eslint-plugin-eslint-plugin": "^4.3.0", "eslint-plugin-flowtype": "^5.8.0 || ^8.0.3", "eslint-plugin-import": "^2.26.0", "estraverse": "^5.3.0", "expect": "^24.9.0", "flow-bin": "^0.147.0", "in-publish": "^2.0.1", "jest": "^24.9.0", "jscodeshift": "^0.7.1", "minimist": "^1.2.7", "npmignore": "^0.3.0", "object.assign": "^4.1.4", "rimraf": "^3.0.2", "safe-publish-latest": "^2.0.0", "to-ast": "^1.0.0"}, "engines": {"node": ">=4.0"}, "license": "MIT", "dependencies": {"@babel/runtime": "^7.20.7", "aria-query": "^5.1.3", "array-includes": "^3.1.6", "array.prototype.flatmap": "^1.3.1", "ast-types-flow": "^0.0.7", "axe-core": "^4.6.2", "axobject-query": "^3.1.1", "damerau-levenshtein": "^1.0.8", "emoji-regex": "^9.2.2", "has": "^1.0.3", "jsx-ast-utils": "^3.3.3", "language-tags": "=1.0.5", "minimatch": "^3.1.2", "object.entries": "^1.1.6", "object.fromentries": "^2.0.6", "semver": "^6.3.0"}, "peerDependencies": {"eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8"}, "jest": {"coverageReporters": ["lcov", "json", "html"], "coverageDirectory": "coverage", "roots": ["__tests__"], "testPathIgnorePatterns": ["__tests__/__util__/"], "testEnvironment": "node"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true, "startingVersion": "6.6.2"}, "publishConfig": {"ignore": ["!lib", ".github/workflows", ".flowconfig", "/src", "/reports", "/flow", "scripts/", "CONTRIBUTING.md"]}}