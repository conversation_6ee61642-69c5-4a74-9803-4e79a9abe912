#!/bin/bash

echo "🔧 Git冲突解决标准流程..."

cat > scripts/conflict-resolver.sh << 'EOF'
#!/bin/bash

echo "🚨 Git冲突解决助手"

# 1. 检查冲突状态
echo "📊 检查当前冲突状态..."
git status

echo ""
echo "🔍 冲突文件列表："
git diff --name-only --diff-filter=U

# 2. 提供解决选项
echo ""
echo "🛠️ 冲突解决选项："
echo "1. 手动解决冲突"
echo "2. 使用合并工具"
echo "3. 接受当前分支的更改"
echo "4. 接受传入分支的更改"
echo "5. 中止合并"

read -p "请选择解决方式 (1-5): " choice

case $choice in
  1)
    echo "📝 手动解决冲突..."
    echo "请编辑冲突文件，删除冲突标记："
    echo "<<<<<<< HEAD"
    echo "======="
    echo ">>>>>>> branch-name"
    echo ""
    echo "解决完成后运行："
    echo "git add ."
    echo "git commit -m 'resolve: 解决合并冲突'"
    ;;
  2)
    echo "🔧 启动合并工具..."
    git mergetool
    ;;
  3)
    echo "✅ 接受当前分支的更改..."
    git checkout --ours .
    git add .
    git commit -m "resolve: 接受当前分支的更改"
    ;;
  4)
    echo "📥 接受传入分支的更改..."
    git checkout --theirs .
    git add .
    git commit -m "resolve: 接受传入分支的更改"
    ;;
  5)
    echo "❌ 中止合并..."
    git merge --abort
    ;;
  *)
    echo "❌ 无效选择"
    exit 1
    ;;
esac
EOF

chmod +x scripts/conflict-resolver.sh

# 创建冲突预防指南
cat > docs/conflict-prevention.md << 'EOF'
# Git冲突预防指南

## 🛡️ 预防策略

### 1. 频繁同步
```bash
# 每天开始工作前同步主分支
git checkout main
git pull origin main
git checkout feature/your-branch
git rebase main
```

### 2. 小步提交
```bash
# 避免大量文件的一次性提交
git add specific-file.js
git commit -m "feat: 添加特定功能"

# 而不是
git add .
git commit -m "feat: 添加很多功能"
```

### 3. 合理分工
- 避免多人同时修改同一文件
- 使用组件化开发减少冲突
- 提前沟通重构计划

## 🔧 冲突解决最佳实践

### 1. 理解冲突类型
```bash
# 内容冲突 - 同一行被不同修改
<<<<<<< HEAD
const apiUrl = 'https://api.hawaiihub.net/v1'
=======
const apiUrl = 'https://api.hawaiihub.net/v2'
>>>>>>> feature/api-upgrade

# 解决方案：选择正确的版本或合并两者
const apiUrl = 'https://api.hawaiihub.net/v2'
```

### 2. 使用工具辅助
```bash
# 配置合并工具
git config --global merge.tool vscode
git config --global mergetool.vscode.cmd 'code --wait $MERGED'

# 或使用其他工具
git config --global merge.tool vimdiff
```

### 3. 验证解决结果
```bash
# 解决冲突后必须验证
npm run test
npm run lint
npm run build
```
EOF

echo "✅ 冲突解决流程设置完成！"
echo "📋 使用方法："
echo "  ./scripts/conflict-resolver.sh  # 冲突解决助手"
echo "  查看文档: docs/conflict-prevention.md"