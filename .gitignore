# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
dev

.vscode
.idea

# docker-compose env files
.env

*.key
*.key.pub

masks.json

# mcp config
app/mcp/mcp_config.json

# HawaiiHub火鸟门户系统特定文件
# 系统核心文件
config.php
database.php
common.inc.php

# 缓存和临时文件
cache/
data/cache/
templates_c/
*.cache
*.compiled

# 日志文件
*.log
logs/
log/
data/logs/
error_log
php_errors.log

# 上传文件
uploads/
static/uploads/
files/

# 备份文件
*.bak
*.backup
*.sql
backup/

# 火鸟系统特定目录
data/
wmsj/
插件/
插件\ 2/
插件.zip
二开插件/

# 临时和测试文件
tmp/
temp/
*.tmp
test_*.php
test_*.py
test_*.sh

# 图片和媒体文件
*.png
*.jpg
*.jpeg
*.gif
*.svg
目录结构.png

# 压缩文件
*.zip
*.tar.gz
*.rar

# Python虚拟环境
venv/
__pycache__/
*.py[cod]
