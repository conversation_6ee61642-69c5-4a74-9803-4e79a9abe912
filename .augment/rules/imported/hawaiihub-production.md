---
alwaysApply: true
description: HawaiiHub火鸟门户系统宝塔生产环境配置规则
type: "always_apply"
---
# HawaiiHub火鸟门户系统宝塔生产环境规则

## 🎯 生产环境概述
HawaiiHub火鸟门户系统运行在宝塔面板管理的Linux服务器上，采用Nginx + PHP-FPM + MySQL架构。

## 🖥️ 服务器环境配置

### 系统信息
- **操作系统**: Linux 6.8.0-49-generic
- **Web服务器**: Nginx 1.24.0
- **PHP版本**: 7.4.33
- **MySQL版本**: 5.7.44-log
- **网站目录**: /www/wwwroot/hawaiihub.net
- **系统版本**: HuoNiaoCMS v8.6 Release utf-8

### 宝塔面板配置
```bash
# 网站根目录权限设置
chown -R www:www /www/wwwroot/hawaiihub.net/
chmod -R 755 /www/wwwroot/hawaiihub.net/
chmod -R 644 /www/wwwroot/hawaiihub.net/*.php

# 日志目录权限
chmod -R 777 /www/wwwroot/hawaiihub.net/log/
chmod -R 777 /www/wwwroot/hawaiihub.net/data/logs/
```

## 🔒 生产环境安全规范

### 文件权限管理
```bash
# 核心文件权限
chmod 644 /www/wwwroot/hawaiihub.net/common.inc.php
chmod 644 /www/wwwroot/hawaiihub.net/config.php
chmod 755 /www/wwwroot/hawaiihub.net/admin/
chmod 644 /www/wwwroot/hawaiihub.net/admin/*.php

# 上传目录权限
chmod -R 777 /www/wwwroot/hawaiihub.net/uploads/
chmod -R 777 /www/wwwroot/hawaiihub.net/static/uploads/

# 缓存目录权限
chmod -R 777 /www/wwwroot/hawaiihub.net/data/cache/
chmod -R 777 /www/wwwroot/hawaiihub.net/templates_c/
```

### 安全配置检查
```php
// 检查生产环境安全设置
if (!defined('IN_HUONIAO')) {
    die('Access Denied');
}

// 关闭调试模式
define('DEBUG', false);

// 设置错误报告级别
error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
ini_set('display_errors', 'Off');
```

### 数据库安全
```sql
-- 生产环境数据库用户权限限制
GRANT SELECT, INSERT, UPDATE, DELETE ON hawaiihub.* TO 'hawaiihub_user'@'localhost';
REVOKE ALL PRIVILEGES ON hawaiihub.* FROM 'hawaiihub_user'@'%';

-- 定期备份数据库
mysqldump -u root -p hawaiihub > /backup/hawaiihub_$(date +%Y%m%d).sql
```

## 🚀 性能优化配置

### Nginx配置优化
```nginx
# /www/server/nginx/conf/vhost/hawaiihub.net.conf
server {
    listen 80;
    server_name hawaiihub.net;
    root /www/wwwroot/hawaiihub.net;
    index index.php index.html;

    # 静态文件缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # PHP文件处理
    location ~ \.php$ {
        fastcgi_pass unix:/tmp/php-cgi-74.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
}
```

### PHP-FPM配置优化
```ini
# /www/server/php/74/etc/php-fpm.d/www.conf
[www]
user = www
group = www
listen = /tmp/php-cgi-74.sock
listen.owner = www
listen.group = www
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 1000
```

### 缓存配置
```php
// 生产环境缓存配置
$cacheConfig = [
    'type' => 'file',  // 文件缓存
    'path' => '/www/wwwroot/hawaiihub.net/data/cache/',
    'expire' => 3600,
    'prefix' => 'hawaiihub_'
];

// Redis缓存（可选）
if (extension_loaded('redis')) {
    $cacheConfig = [
        'type' => 'redis',
        'host' => '127.0.0.1',
        'port' => 6379,
        'expire' => 3600
    ];
}
```

## 📊 监控与日志

### 错误日志配置
```php
// 生产环境日志配置
ini_set('log_errors', 'On');
ini_set('error_log', '/www/wwwroot/hawaiihub.net/log/php_errors.log');

// 自定义错误处理
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    $logMessage = date('Y-m-d H:i:s') . " [$errno] $errstr in $errfile on line $errline\n";
    error_log($logMessage, 3, '/www/wwwroot/hawaiihub.net/log/custom_errors.log');
    return true;
}
set_error_handler('customErrorHandler');
```

### 访问日志分析
```bash
# 分析Nginx访问日志
tail -f /www/wwwlogs/hawaiihub.net.log | grep -E "(404|500|403)"

# 分析PHP错误日志
tail -f /www/wwwroot/hawaiihub.net/log/php_errors.log

# 分析火鸟系统日志
tail -f /www/wwwroot/hawaiihub.net/log/errorsql/*.log
```

## 🔧 维护任务

### 定时任务配置
```bash
# 宝塔面板定时任务
# 数据库备份
0 2 * * * mysqldump -u root -p hawaiihub > /backup/hawaiihub_$(date +\%Y\%m\%d).sql

# 清理缓存
0 3 * * * find /www/wwwroot/hawaiihub.net/data/cache/ -type f -mtime +7 -delete

# 清理日志
0 4 * * * find /www/wwwroot/hawaiihub.net/log/ -name "*.log" -mtime +30 -delete
```

### 系统监控
```bash
# 检查磁盘空间
df -h /www/wwwroot/hawaiihub.net/

# 检查内存使用
free -h

# 检查PHP进程
ps aux | grep php-fpm

# 检查MySQL状态
systemctl status mysqld
```

## 🚨 故障处理

### 常见问题排查
```bash
# 网站无法访问
systemctl status nginx
systemctl status php-fpm-74
systemctl status mysqld

# 权限问题
ls -la /www/wwwroot/hawaiihub.net/
chown -R www:www /www/wwwroot/hawaiihub.net/

# 数据库连接问题
mysql -u root -p -e "SHOW PROCESSLIST;"
```

### 紧急恢复流程
```bash
# 1. 停止服务
systemctl stop nginx
systemctl stop php-fpm-74

# 2. 备份当前状态
cp -r /www/wwwroot/hawaiihub.net/ /backup/hawaiihub_emergency_$(date +%Y%m%d_%H%M%S)/

# 3. 恢复备份
tar -xzf /backup/hawaiihub_latest.tar.gz -C /www/wwwroot/

# 4. 修复权限
chown -R www:www /www/wwwroot/hawaiihub.net/
chmod -R 755 /www/wwwroot/hawaiihub.net/

# 5. 重启服务
systemctl start php-fpm-74
systemctl start nginx
```

## 📋 部署检查清单

### 上线前检查
- [ ] 文件权限设置正确
- [ ] 数据库连接正常
- [ ] 缓存目录可写
- [ ] 日志目录可写
- [ ] SSL证书配置正确
- [ ] 防火墙规则设置
- [ ] 备份策略已配置
- [ ] 监控告警已设置

### 性能检查
- [ ] Nginx配置优化
- [ ] PHP-FPM参数调优
- [ ] MySQL查询优化
- [ ] 静态文件缓存
- [ ] 图片压缩处理
- [ ] CDN配置（如需要）

---

**基于宝塔生产环境实际需求制定，最后