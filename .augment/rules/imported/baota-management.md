---
type: "manual"
---

# 宝塔面板管理规则

## 🎯 宝塔面板概述
宝塔面板是Linux服务器管理面板，用于管理HawaiiHub火鸟门户系统的Web服务器、数据库、文件等。

## 🖥️ 宝塔面板访问

### 面板信息
- **面板地址**: http://服务器IP:8888
- **默认账号**: admin
- **默认密码**: 安装时设置
- **安全入口**: 建议开启

### 安全设置
```bash
# 修改面板端口
bt default

# 修改面板密码
bt default

# 开启安全入口
bt default

# 设置防火墙
firewall-cmd --permanent --add-port=8888/tcp
firewall-cmd --reload
```

## 📁 网站管理

### 网站配置
- **网站名称**: hawaiihub.net
- **网站目录**: /www/wwwroot/hawaiihub.net
- **域名**: hawaiihub.net
- **PHP版本**: 7.4

### 网站设置步骤
1. **创建网站**
   - 登录宝塔面板
   - 点击"网站" → "添加站点"
   - 域名: hawaiihub.net
   - 根目录: /www/wwwroot/hawaiihub.net
   - PHP版本: 7.4

2. **配置SSL证书**
   - 点击网站 → "SSL"
   - 选择"Let's Encrypt"免费证书
   - 开启"强制HTTPS"

3. **配置伪静态**
   - 点击网站 → "设置" → "伪静态"
   - 选择"火鸟门户系统"规则

## 🔧 服务器管理

### 软件管理
```bash
# 安装必要软件
yum install -y nginx
yum install -y php74
yum install -y mysql57

# 启动服务
systemctl start nginx
systemctl start php-fpm-74
systemctl start mysqld

# 设置开机自启
systemctl enable nginx
systemctl enable php-fpm-74
systemctl enable mysqld
```

### 性能优化
```bash
# 优化PHP配置
sed -i 's/memory_limit = 128M/memory_limit = 256M/' /www/server/php/74/etc/php.ini
sed -i 's/max_execution_time = 30/max_execution_time = 300/' /www/server/php/74/etc/php.ini

# 优化MySQL配置
sed -i 's/max_connections = 151/max_connections = 500/' /etc/my.cnf
sed -i 's/innodb_buffer_pool_size = 128M/innodb_buffer_pool_size = 512M/' /etc/my.cnf

# 重启服务
systemctl restart php-fpm-74
systemctl restart mysqld
```

## 📊 监控与维护

### 系统监控
```bash
# 查看系统资源
htop
df -h
free -h

# 查看服务状态
systemctl status nginx
systemctl status php-fpm-74
systemctl status mysqld

# 查看日志
tail -f /www/wwwlogs/hawaiihub.net.log
tail -f /www/server/php/74/var/log/php-fpm.log
```

### 备份策略
```bash
# 创建备份脚本
cat > /root/backup_hawaiihub.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/hawaiihub"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份网站文件
tar -czf $BACKUP_DIR/hawaiihub_files_$DATE.tar.gz -C /www/wwwroot hawaiihub.net

# 备份数据库
mysqldump -u root -p hawaiihub > $BACKUP_DIR/hawaiihub_db_$DATE.sql

# 清理7天前的备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete

echo "备份完成: $DATE"
EOF

chmod +x /root/backup_hawaiihub.sh
```

## 🚨 故障处理

### 常见问题解决

#### 1. 网站无法访问
```bash
# 检查Nginx状态
systemctl status nginx

# 检查配置文件
nginx -t

# 重启Nginx
systemctl restart nginx

# 检查防火墙
firewall-cmd --list-all
```

#### 2. PHP错误
```bash
# 检查PHP-FPM状态
systemctl status php-fpm-74

# 查看PHP错误日志
tail -f /www/server/php/74/var/log/php-fpm.log

# 重启PHP-FPM
systemctl restart php-fpm-74
```

#### 3. 数据库连接失败
```bash
# 检查MySQL状态
systemctl status mysqld

# 检查MySQL进程
ps aux | grep mysql

# 重启MySQL
systemctl restart mysqld

# 检查数据库连接
mysql -u root -p -e "SHOW DATABASES;"
```

### 紧急恢复
```bash
# 1. 停止所有服务
systemctl stop nginx
systemctl stop php-fpm-74
systemctl stop mysqld

# 2. 备份当前状态
cp -r /www/wwwroot/hawaiihub.net /backup/emergency_$(date +%Y%m%d_%H%M%S)/

# 3. 恢复备份
tar -xzf /backup/hawaiihub_files_latest.tar.gz -C /www/wwwroot/
mysql -u root -p hawaiihub < /backup/hawaiihub_db_latest.sql

# 4. 修复权限
chown -R www:www /www/wwwroot/hawaiihub.net/
chmod -R 755 /www/wwwroot/hawaiihub.net/

# 5. 重启服务
systemctl start mysqld
systemctl start php-fpm-74
systemctl start nginx
```

## 📋 日常维护任务

### 每日检查
- [ ] 网站访问正常
- [ ] 数据库连接正常
- [ ] 磁盘空间充足
- [ ] 内存使用正常
- [ ] 错误日志无异常

### 每周维护
- [ ] 清理临时文件
- [ ] 检查安全更新
- [ ] 备份数据
- [ ] 优化数据库
- [ ] 检查SSL证书

### 每月维护
- [ ] 系统安全更新
- [ ] 性能优化检查
- [ ] 备份策略验证
- [ ] 监控告警测试
- [ ] 安全扫描

## 🔒 安全配置

### 防火墙设置
```bash
# 开启防火墙
systemctl start firewalld
systemctl enable firewalld

# 开放必要端口
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --permanent --add-port=8888/tcp
firewall-cmd --reload
```

### 安全加固
```bash
# 修改SSH端口
sed -i 's/#Port 22/Port 2222/' /etc/ssh/sshd_config
systemctl restart sshd

# 禁用root远程登录
sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
systemctl restart sshd

# 设置SSH密钥认证
mkdir -p ~/.ssh
chmod 700 ~/.ssh
```

## 📞 技术支持

### 宝塔官方支持
- **官网**: https://www.bt.cn/
- **论坛**: https://www.bt.cn/bbs/
- **文档**: https://www.bt.cn/bbs/thread-19376-1-1.html

### 火鸟系统支持
- **官网**: https://www.kumanyun.com/
- **论坛**: http://bbs.kumanyun.com/
- **帮助中心**: https://help.kumanyun.com/

---

**基于宝塔面板实际管理需求制定，最后更新时间：2025-01-28**
description:
globs:
alwaysApply: false
---
