{"name": "webpack", "version": "5.88.1", "author": "<PERSON> @sokra", "description": "Packs ECMAScript/CommonJs/AMD modules for the browser. Allows you to split your codebase into multiple bundles, which can be loaded on demand. Supports loaders to preprocess files, i.e. json, jsx, es7, css, less, ... and your custom stuff.", "license": "MIT", "dependencies": {"@types/eslint-scope": "^3.7.3", "@types/estree": "^1.0.0", "@webassemblyjs/ast": "^1.11.5", "@webassemblyjs/wasm-edit": "^1.11.5", "@webassemblyjs/wasm-parser": "^1.11.5", "acorn": "^8.7.1", "acorn-import-assertions": "^1.9.0", "browserslist": "^4.14.5", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^5.15.0", "es-module-lexer": "^1.2.1", "eslint-scope": "5.1.1", "events": "^3.2.0", "glob-to-regexp": "^0.4.1", "graceful-fs": "^4.2.9", "json-parse-even-better-errors": "^2.3.1", "loader-runner": "^4.2.0", "mime-types": "^2.1.27", "neo-async": "^2.6.2", "schema-utils": "^3.2.0", "tapable": "^2.1.1", "terser-webpack-plugin": "^5.3.7", "watchpack": "^2.4.0", "webpack-sources": "^3.2.3"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}, "devDependencies": {"@babel/core": "^7.21.4", "@babel/preset-react": "^7.18.6", "@types/jest": "^29.5.0", "@types/mime-types": "^2.1.1", "@types/node": "^20.1.7", "assemblyscript": "^0.27.2", "babel-loader": "^8.1.0", "benchmark": "^2.1.4", "bundle-loader": "^0.5.6", "coffee-loader": "^1.0.0", "coffeescript": "^2.5.1", "core-js": "^3.6.5", "coveralls": "^3.1.0", "cspell": "^6.31.1", "css-loader": "^5.0.1", "date-fns": "^2.15.0", "es5-ext": "^0.10.53", "es6-promise-polyfill": "^1.2.0", "eslint": "^8.38.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-jsdoc": "^43.0.5", "eslint-plugin-node": "^11.0.0", "eslint-plugin-prettier": "^4.2.1", "file-loader": "^6.0.0", "fork-ts-checker-webpack-plugin": "^8.0.0", "hash-wasm": "^4.9.0", "husky": "^8.0.3", "is-ci": "^3.0.0", "istanbul": "^0.4.5", "jest": "^29.5.0", "jest-circus": "^29.5.0", "jest-cli": "^29.5.0", "jest-diff": "^29.5.0", "jest-environment-node": "^29.5.0", "jest-junit": "^16.0.0", "json-loader": "^0.5.7", "json5": "^2.1.3", "less": "^4.0.0", "less-loader": "^8.0.0", "lint-staged": "^13.2.1", "lodash": "^4.17.19", "lodash-es": "^4.17.15", "memfs": "^3.5.0", "mini-css-extract-plugin": "^1.6.1", "mini-svg-data-uri": "^1.2.3", "nyc": "^15.1.0", "open-cli": "^7.2.0", "prettier": "^2.7.1", "pretty-format": "^29.5.0", "pug": "^3.0.0", "pug-loader": "^2.4.0", "raw-loader": "^4.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^3.0.2", "script-loader": "^0.7.2", "simple-git": "^3.17.0", "strip-ansi": "^6.0.0", "style-loader": "^2.0.0", "terser": "^5.17.0", "toml": "^3.0.0", "tooling": "webpack/tooling#v1.23.0", "ts-loader": "^9.4.2", "typescript": "^5.0.4", "url-loader": "^4.1.0", "wast-loader": "^1.11.5", "webassembly-feature": "1.3.0", "webpack-cli": "^5.0.1", "xxhashjs": "^0.2.2", "yamljs": "^0.3.0", "yarn-deduplicate": "^6.0.1"}, "engines": {"node": ">=10.13.0"}, "repository": {"type": "git", "url": "https://github.com/webpack/webpack.git"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "homepage": "https://github.com/webpack/webpack", "bugs": "https://github.com/webpack/webpack/issues", "main": "lib/index.js", "bin": {"webpack": "bin/webpack.js"}, "types": "types.d.ts", "files": ["lib/", "bin/", "hot/", "schemas/", "SECURITY.md", "module.d.ts", "types.d.ts"], "scripts": {"setup": "node ./setup/setup.js", "jest": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation node_modules/jest-cli/bin/jest --logHeapUsage", "test": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation node_modules/jest-cli/bin/jest --logHeapUsage", "test:update-snapshots": "yarn jest -u", "test:integration": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation node_modules/jest-cli/bin/jest --logHeapUsage --testMatch \"<rootDir>/test/*.{basictest,longtest,test}.js\"", "test:basic": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation node_modules/jest-cli/bin/jest --logHeapUsage --testMatch \"<rootDir>/test/*.basictest.js\"", "test:unit": "node --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.unittest.js\"", "build:examples": "cd examples && node buildAll.js", "type-report": "rimraf coverage && yarn cover:types && yarn cover:report && open-cli coverage/lcov-report/index.html", "pretest": "yarn lint", "prelint": "yarn setup", "lint": "yarn code-lint && yarn special-lint && yarn type-lint && yarn typings-test && yarn module-typings-test && yarn yarn-lint && yarn pretty-lint && yarn spellcheck", "code-lint": "eslint --cache .", "type-lint": "tsc", "typings-test": "tsc -p tsconfig.types.test.json", "module-typings-test": "tsc -p tsconfig.module.test.json", "spellcheck": "cspell --no-progress \"**\"", "special-lint": "node node_modules/tooling/lockfile-lint && node node_modules/tooling/schemas-lint && node node_modules/tooling/inherit-types && node node_modules/tooling/format-schemas && node tooling/generate-runtime-code.js && node tooling/generate-wasm-code.js && node node_modules/tooling/format-file-header && node node_modules/tooling/compile-to-definitions && node node_modules/tooling/precompile-schemas && node node_modules/tooling/generate-types --no-template-literals", "special-lint-fix": "node node_modules/tooling/inherit-types --write && node node_modules/tooling/format-schemas --write && node tooling/generate-runtime-code.js --write && node tooling/generate-wasm-code.js --write && node node_modules/tooling/format-file-header --write && node node_modules/tooling/compile-to-definitions --write && node node_modules/tooling/precompile-schemas --write && node node_modules/tooling/generate-types --no-template-literals --write", "fix": "yarn code-lint --fix && yarn special-lint-fix && yarn pretty-lint-fix", "prepare": "husky install", "pretty-lint-base": "prettier --cache .", "pretty-lint-fix": "yarn pretty-lint-base --loglevel warn --write", "pretty-lint": "yarn pretty-lint-base --check", "yarn-lint": "yarn-deduplicate --fail --list -s highest yarn.lock", "yarn-lint-fix": "yarn-deduplicate -s highest yarn.lock", "benchmark": "node --max-old-space-size=4096 --experimental-vm-modules --trace-deprecation node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.benchmark.js\" --runInBand", "cover": "yarn cover:all && yarn cover:report", "cover:clean": "rimraf .nyc_output coverage", "cover:all": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules node_modules/jest-cli/bin/jest --logHeapUsage --coverage", "cover:basic": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules node_modules/jest-cli/bin/jest --logHeapUsage --testMatch \"<rootDir>/test/*.basictest.js\" --coverage", "cover:integration": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules node_modules/jest-cli/bin/jest --logHeapUsage --testMatch \"<rootDir>/test/*.{basictest,longtest,test}.js\" --coverage", "cover:integration:a": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules node_modules/jest-cli/bin/jest --logHeapUsage --testMatch \"<rootDir>/test/*.{basictest,test}.js\" --coverage", "cover:integration:b": "node --expose-gc --max-old-space-size=4096 --experimental-vm-modules node_modules/jest-cli/bin/jest --logHeapUsage --testMatch \"<rootDir>/test/*.longtest.js\" --coverage", "cover:unit": "node --max-old-space-size=4096 --experimental-vm-modules node_modules/jest-cli/bin/jest --testMatch \"<rootDir>/test/*.unittest.js\" --coverage", "cover:types": "node node_modules/tooling/type-coverage", "cover:merge": "yarn mkdirp .nyc_output && nyc merge .nyc_output coverage/coverage-nyc.json && rimraf .nyc_output", "cover:report": "nyc report --reporter=lcov  --reporter=text -t coverage"}, "lint-staged": {"*.{js,cjs,mjs}": ["eslint --cache --fix"], "*": ["prettier --cache --ignore-unknown"], "*.md|{.github,benchmark,bin,examples,hot,lib,schemas,setup,tooling}/**/*.{md,yml,yaml,js,json}": ["cspell"]}, "jest": {"forceExit": true, "setupFilesAfterEnv": ["<rootDir>/test/setupTestFramework.js"], "testMatch": ["<rootDir>/test/*.test.js", "<rootDir>/test/*.basictest.js", "<rootDir>/test/*.longtest.js", "<rootDir>/test/*.unittest.js"], "watchPathIgnorePatterns": ["<rootDir>/.git", "<rootDir>/node_modules", "<rootDir>/test/js", "<rootDir>/test/browsertest/js", "<rootDir>/test/fixtures/temp-cache-fixture", "<rootDir>/test/fixtures/temp-", "<rootDir>/benchmark", "<rootDir>/assembly", "<rootDir>/tooling", "<rootDir>/examples/*/dist", "<rootDir>/coverage", "<rootDir>/.eslintcache"], "modulePathIgnorePatterns": ["<rootDir>/.git", "<rootDir>/node_modules/webpack/node_modules", "<rootDir>/test/js", "<rootDir>/test/browsertest/js", "<rootDir>/test/fixtures/temp-cache-fixture", "<rootDir>/test/fixtures/temp-", "<rootDir>/benchmark", "<rootDir>/examples/*/dist", "<rootDir>/coverage", "<rootDir>/.eslintcache"], "transformIgnorePatterns": ["<rootDir>"], "coverageDirectory": "<rootDir>/coverage", "coveragePathIgnorePatterns": ["\\.runtime\\.js$", "<rootDir>/test", "<rootDir>/schemas", "<rootDir>/node_modules"], "testEnvironment": "./test/patch-node-env.js", "coverageReporters": ["json"], "snapshotFormat": {"escapeString": true, "printBasicPrototype": true}}}