import type { server } from 'typescript/lib/tsserverlibrary';
import { type ASTAndDefiniteProgram } from './create-program/shared';
import type { MutableParseSettings } from './parseSettings';
export declare function useProgramFromProjectService(projectService: server.ProjectService, parseSettings: Readonly<MutableParseSettings>): ASTAndDefiniteProgram | undefined;
//# sourceMappingURL=useProgramFromProjectService.d.ts.map