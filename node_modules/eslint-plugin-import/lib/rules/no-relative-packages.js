'use strict';var _path = require('path');var _path2 = _interopRequireDefault(_path);
var _readPkgUp = require('eslint-module-utils/readPkgUp');var _readPkgUp2 = _interopRequireDefault(_readPkgUp);

var _resolve = require('eslint-module-utils/resolve');var _resolve2 = _interopRequireDefault(_resolve);
var _moduleVisitor = require('eslint-module-utils/moduleVisitor');var _moduleVisitor2 = _interopRequireDefault(_moduleVisitor);
var _importType = require('../core/importType');var _importType2 = _interopRequireDefault(_importType);
var _docsUrl = require('../docsUrl');var _docsUrl2 = _interopRequireDefault(_docsUrl);function _interopRequireDefault(obj) {return obj && obj.__esModule ? obj : { 'default': obj };}

/** @param {string} filePath */
function toPosixPath(filePath) {
  return filePath.replace(/\\/g, '/');
}

function findNamedPackage(filePath) {
  var found = (0, _readPkgUp2['default'])({ cwd: filePath });
  if (found.pkg && !found.pkg.name) {
    return findNamedPackage(_path2['default'].join(found.path, '../..'));
  }
  return found;
}

function checkImportForRelativePackage(context, importPath, node) {
  var potentialViolationTypes = ['parent', 'index', 'sibling'];
  if (potentialViolationTypes.indexOf((0, _importType2['default'])(importPath, context)) === -1) {
    return;
  }

  var resolvedImport = (0, _resolve2['default'])(importPath, context);
  var resolvedContext = context.getPhysicalFilename ? context.getPhysicalFilename() : context.getFilename();

  if (!resolvedImport || !resolvedContext) {
    return;
  }

  var importPkg = findNamedPackage(resolvedImport);
  var contextPkg = findNamedPackage(resolvedContext);

  if (importPkg.pkg && contextPkg.pkg && importPkg.pkg.name !== contextPkg.pkg.name) {
    var importBaseName = _path2['default'].basename(importPath);
    var importRoot = _path2['default'].dirname(importPkg.path);
    var properPath = _path2['default'].relative(importRoot, resolvedImport);
    var properImport = _path2['default'].join(
    importPkg.pkg.name,
    _path2['default'].dirname(properPath),
    importBaseName === _path2['default'].basename(importRoot) ? '' : importBaseName);

    context.report({
      node: node,
      message: 'Relative import from another package is not allowed. Use `' + String(properImport) + '` instead of `' + String(importPath) + '`',
      fix: function () {function fix(fixer) {return fixer.replaceText(node, JSON.stringify(toPosixPath(properImport)));}return fix;}() });


  }
}

module.exports = {
  meta: {
    type: 'suggestion',
    docs: {
      category: 'Static analysis',
      description: 'Forbid importing packages through relative paths.',
      url: (0, _docsUrl2['default'])('no-relative-packages') },

    fixable: 'code',
    schema: [(0, _moduleVisitor.makeOptionsSchema)()] },


  create: function () {function create(context) {
      return (0, _moduleVisitor2['default'])(function (source) {return checkImportForRelativePackage(context, source.value, source);}, context.options[0]);
    }return create;}() };
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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