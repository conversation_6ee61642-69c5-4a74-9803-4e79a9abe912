---
type: "manual"
---

# 火鸟门户系统生产环境部署规则

## 🎯 系统概述
火鸟门户系统（HuoNiaoCMS）是一个基于PHP的企业级门户网站系统，支持多模块、多插件架构。

### 系统信息
- **系统版本**: HuoNiaoCMS v8.6 Release utf-8
- **技术支持**: 苏州酷曼软件技术有限公司
- **官方网站**: https://www.kumanyun.com/
- **演示网站**: https://ihuoniao.cn/sz

## 📦 系统安装

### 环境要求
```bash
# 系统要求
- Linux 6.8.0-49-generic
- Nginx 1.24.0
- PHP 7.4.33
- MySQL 5.7.44-log
- 内存: 2GB+
- 磁盘: 10GB+
```

### 安装步骤
```bash
# 1. 下载系统文件
wget https://www.kumanyun.com/download/huoniao_v8.6.zip
unzip huoniao_v8.6.zip

# 2. 上传到服务器
scp -r huoniao/* root@服务器IP:/www/wwwroot/hawaiihub.net/

# 3. 设置权限
chown -R www:www /www/wwwroot/hawaiihub.net/
chmod -R 755 /www/wwwroot/hawaiihub.net/
chmod -R 777 /www/wwwroot/hawaiihub.net/data/
chmod -R 777 /www/wwwroot/hawaiihub.net/log/
chmod -R 777 /www/wwwroot/hawaiihub.net/uploads/
```

### 数据库配置
```sql
-- 创建数据库
CREATE DATABASE hawaiihub CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'hawaiihub_user'@'localhost' IDENTIFIED BY '安全密码';
GRANT ALL PRIVILEGES ON hawaiihub.* TO 'hawaiihub_user'@'localhost';
FLUSH PRIVILEGES;
```

## ⚙️ 系统配置

### 基础配置文件
```php
// /www/wwwroot/hawaiihub.net/config.php
<?php
// 数据库配置
$db_host = 'localhost';
$db_name = 'hawaiihub';
$db_user = 'hawaiihub_user';
$db_pass = '安全密码';
$db_port = 3306;

// 系统配置
$site_name = '华人中心';
$site_url = 'https://hawaiihub.net';
$admin_email = '<EMAIL>';

// 安全配置
$debug = false;
$log_level = 'ERROR';
?>
```

### 伪静态配置
```nginx
# Nginx伪静态规则
location / {
    try_files $uri $uri/ /index.php?$query_string;
}

# 后台伪静态
location /admin/ {
    try_files $uri $uri/ /admin/index.php?$query_string;
}

# API伪静态
location /api/ {
    try_files $uri $uri/ /api/index.php?$query_string;
}
```

## 🔧 性能优化

### PHP优化配置
```ini
# /www/server/php/74/etc/php.ini
[PHP]
memory_limit = 256M
max_execution_time = 300
max_input_time = 300
post_max_size = 50M
upload_max_filesize = 50M
max_file_uploads = 20

# 错误处理
display_errors = Off
log_errors = On
error_log = /www/wwwroot/hawaiihub.net/log/php_errors.log

# 缓存配置
opcache.enable = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1
```

### MySQL优化配置
```ini
# /etc/my.cnf
[mysqld]
# 连接数设置
max_connections = 500
max_connect_errors = 1000

# 内存设置
innodb_buffer_pool_size = 512M
innodb_log_file_size = 64M
innodb_log_buffer_size = 16M

# 查询缓存
query_cache_type = 1
query_cache_size = 64M
query_cache_limit = 2M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

### Nginx优化配置
```nginx
# /www/server/nginx/conf/nginx.conf
http {
    # 基础设置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 缓存设置
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 🔒 安全配置

### 文件权限设置
```bash
# 核心文件权限
chmod 644 /www/wwwroot/hawaiihub.net/config.php
chmod 644 /www/wwwroot/hawaiihub.net/common.inc.php
chmod 755 /www/wwwroot/hawaiihub.net/admin/
chmod 644 /www/wwwroot/hawaiihub.net/admin/*.php

# 上传目录权限
chmod -R 777 /www/wwwroot/hawaiihub.net/uploads/
chmod -R 777 /www/wwwroot/hawaiihub.net/static/uploads/

# 缓存目录权限
chmod -R 777 /www/wwwroot/hawaiihub.net/data/cache/
chmod -R 777 /www/wwwroot/hawaiihub.net/templates_c/
```

### 安全头设置
```nginx
# 安全响应头
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';" always;
```

### 防火墙配置
```bash
# 设置防火墙规则
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --permanent --remove-port=22/tcp
firewall-cmd --permanent --add-port=2222/tcp
firewall-cmd --reload
```

## 📊 监控配置

### 日志配置
```php
// 自定义日志函数
function writeLog($type, $message, $level = 'INFO') {
    $logFile = '/www/wwwroot/hawaiihub.net/log/' . $type . '_' . date('Y-m-d') . '.log';
    $logMessage = date('Y-m-d H:i:s') . " [$level] $message\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

// 使用示例
writeLog('system', '系统启动', 'INFO');
writeLog('error', '数据库连接失败', 'ERROR');
writeLog('security', '用户登录失败: admin', 'WARNING');
```

### 性能监控
```bash
# 创建监控脚本
cat > /root/monitor_hawaiihub.sh << 'EOF'
#!/bin/bash
LOG_FILE="/www/wwwroot/hawaiihub.net/log/monitor.log"

# 检查服务状态
check_service() {
    local service=$1
    if systemctl is-active --quiet $service; then
        echo "$(date): $service 运行正常" >> $LOG_FILE
    else
        echo "$(date): $service 服务异常" >> $LOG_FILE
        systemctl restart $service
    fi
}

# 检查磁盘空间
check_disk() {
    local usage=$(df /www/wwwroot/hawaiihub.net/ | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $usage -gt 80 ]; then
        echo "$(date): 磁盘使用率过高: ${usage}%" >> $LOG_FILE
    fi
}

# 检查内存使用
check_memory() {
    local usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ $usage -gt 80 ]; then
        echo "$(date): 内存使用率过高: ${usage}%" >> $LOG_FILE
    fi
}

# 执行检查
check_service nginx
check_service php-fpm-74
check_service mysqld
check_disk
check_memory
EOF

chmod +x /root/monitor_hawaiihub.sh
```

## 🔄 备份策略

### 自动备份脚本
```bash
# 创建备份脚本
cat > /root/backup_hawaiihub.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/hawaiihub"
SITE_DIR="/www/wwwroot/hawaiihub.net"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份网站文件
tar -czf $BACKUP_DIR/hawaiihub_files_$DATE.tar.gz -C /www/wwwroot hawaiihub.net

# 备份数据库
mysqldump -u root -p hawaiihub > $BACKUP_DIR/hawaiihub_db_$DATE.sql

# 压缩数据库备份
gzip $BACKUP_DIR/hawaiihub_db_$DATE.sql

# 清理7天前的备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "备份完成: $DATE" >> $BACKUP_DIR/backup.log
EOF

chmod +x /root/backup_hawaiihub.sh
```

### 定时任务设置
```bash
# 添加定时任务
crontab -e

# 每日凌晨2点备份
0 2 * * * /root/backup_hawaiihub.sh

# 每5分钟检查服务状态
*/5 * * * * /root/monitor_hawaiihub.sh

# 每周日凌晨3点清理缓存
0 3 * * 0 find /www/wwwroot/hawaiihub.net/data/cache/ -type f -mtime +7 -delete
```

## 🚨 故障恢复

### 快速恢复流程
```bash
# 1. 停止服务
systemctl stop nginx
systemctl stop php-fpm-74
systemctl stop mysqld

# 2. 备份当前状态
cp -r /www/wwwroot/hawaiihub.net /backup/emergency_$(date +%Y%m%d_%H%M%S)/

# 3. 恢复文件
tar -xzf /backup/hawaiihub_files_latest.tar.gz -C /www/wwwroot/

# 4. 恢复数据库
gunzip -c /backup/hawaiihub_db_latest.sql.gz | mysql -u root -p hawaiihub

# 5. 修复权限
chown -R www:www /www/wwwroot/hawaiihub.net/
chmod -R 755 /www/wwwroot/hawaiihub.net/

# 6. 重启服务
systemctl start mysqld
systemctl start php-fpm-74
systemctl start nginx
```

## 📋 部署检查清单

### 安装前检查
- [ ] 服务器环境满足要求
- [ ] 域名解析正确
- [ ] SSL证书已申请
- [ ] 数据库已创建
- [ ] 备份策略已制定

### 安装后检查
- [ ] 网站可以正常访问
- [ ] 后台可以正常登录
- [ ] 数据库连接正常
- [ ] 文件权限设置正确
- [ ] 日志记录正常
- [ ] 备份功能正常
- [ ] 监控告警已设置

### 性能优化检查
- [ ] PHP配置已优化
- [ ] MySQL配置已优化
- [ ] Nginx配置已优化
- [ ] 缓存已启用
- [ ] 静态文件已压缩
- [ ] 安全配置已设置

---

**基于火鸟门户系统生产环境实际需求制定，最后更新时间：2025-01-28**
description:
globs:
alwaysApply: false
---
