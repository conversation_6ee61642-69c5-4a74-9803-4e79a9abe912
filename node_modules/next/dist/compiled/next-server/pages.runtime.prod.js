(()=>{var e={"./dist/compiled/@edge-runtime/cookies/index.js":e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,s={};function i(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean);return`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}; ${r.join("; ")}`}function a(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,o]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=o?o:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[n,o],...s]=a(e),{domain:i,expires:l,httponly:c,maxage:p,path:h,samesite:f,secure:m,priority:g}=Object.fromEntries(s.map(([e,t])=>[e.toLowerCase(),t])),v={name:n,value:decodeURIComponent(o),domain:i,...l&&{expires:new Date(l)},...c&&{httpOnly:!0},..."string"==typeof p&&{maxAge:Number(p)},path:h,...f&&{sameSite:d.includes(t=(t=f).toLowerCase())?t:void 0},...m&&{secure:!0},...g&&{priority:u.includes(r=(r=g).toLowerCase())?r:void 0}};return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}(v)}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(s,{RequestCookies:()=>c,ResponseCookies:()=>p,parseCookie:()=>a,parseSetCookie:()=>l,stringifyCookie:()=>i}),e.exports=((e,s,i,a)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let i of n(s))o.call(e,i)||void 0===i||t(e,i,{get:()=>s[i],enumerable:!(a=r(s,i))||a.enumerable});return e})(t({},"__esModule",{value:!0}),s);var d=["strict","lax","none"],u=["low","medium","high"],c=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t){let e=a(t);for(let[t,r]of e)this._parsed.set(t,{name:t,value:r})}}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>i(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>i(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},p=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let o=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[],s=Array.isArray(o)?o:function(e){if(!e)return[];var t,r,n,o,s,i=[],a=0;function l(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,s=!1;l();)if(","===(r=e.charAt(a))){for(n=a,a+=1,l(),o=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(s=!0,a=o,i.push(e.substring(t,n)),t=a):a=n+1}else a+=1;(!s||a>=e.length)&&i.push(e.substring(t,e.length))}return i}(o);for(let e of s){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,o=this._parsed;return o.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=i(r);t.append("set-cookie",e)}}(o,this._headers),this}delete(...e){let[t,r,n]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:n,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(i).join("; ")}}},"./dist/compiled/cookie/index.js":e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var o={},s=t.split(n),i=(r||{}).decode||e,a=0;a<s.length;a++){var l=s[a],d=l.indexOf("=");if(!(d<0)){var u=l.substr(0,d).trim(),c=l.substr(++d,l.length).trim();'"'==c[0]&&(c=c.slice(1,-1)),void 0==o[u]&&(o[u]=function(e,t){try{return t(e)}catch(t){return e}}(c,i))}}return o},t.serialize=function(e,t,n){var s=n||{},i=s.encode||r;if("function"!=typeof i)throw TypeError("option encode is invalid");if(!o.test(e))throw TypeError("argument name is invalid");var a=i(t);if(a&&!o.test(a))throw TypeError("argument val is invalid");var l=e+"="+a;if(null!=s.maxAge){var d=s.maxAge-0;if(isNaN(d)||!isFinite(d))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(d)}if(s.domain){if(!o.test(s.domain))throw TypeError("option domain is invalid");l+="; Domain="+s.domain}if(s.path){if(!o.test(s.path))throw TypeError("option path is invalid");l+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(l+="; HttpOnly"),s.secure&&(l+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,o=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},"./dist/compiled/react-is/cjs/react-is.production.min.js":(e,t)=>{"use strict";/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),d=Symbol.for("react.context"),u=Symbol.for("react.server_context"),c=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),f=Symbol.for("react.memo"),m=Symbol.for("react.lazy"),g=Symbol.for("react.offscreen");function v(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case s:case a:case i:case p:case h:return e;default:switch(e=e&&e.$$typeof){case u:case d:case c:case m:case f:case l:return e;default:return t}}case o:return t}}}r=Symbol.for("react.module.reference"),t.ContextConsumer=d,t.ContextProvider=l,t.Element=n,t.ForwardRef=c,t.Fragment=s,t.Lazy=m,t.Memo=f,t.Portal=o,t.Profiler=a,t.StrictMode=i,t.Suspense=p,t.SuspenseList=h,t.isAsyncMode=function(){return!1},t.isConcurrentMode=function(){return!1},t.isContextConsumer=function(e){return v(e)===d},t.isContextProvider=function(e){return v(e)===l},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},t.isForwardRef=function(e){return v(e)===c},t.isFragment=function(e){return v(e)===s},t.isLazy=function(e){return v(e)===m},t.isMemo=function(e){return v(e)===f},t.isPortal=function(e){return v(e)===o},t.isProfiler=function(e){return v(e)===a},t.isStrictMode=function(e){return v(e)===i},t.isSuspense=function(e){return v(e)===p},t.isSuspenseList=function(e){return v(e)===h},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===s||e===a||e===i||e===p||e===h||e===g||"object"==typeof e&&null!==e&&(e.$$typeof===m||e.$$typeof===f||e.$$typeof===l||e.$$typeof===d||e.$$typeof===c||e.$$typeof===r||void 0!==e.getModuleId)},t.typeOf=v},"./dist/compiled/react-is/index.js":(e,t,r)=>{"use strict";e.exports=r("./dist/compiled/react-is/cjs/react-is.production.min.js")},"./dist/compiled/strip-ansi/index.js":e=>{(()=>{"use strict";var t={511:e=>{e.exports=({onlyFirst:e=!1}={})=>RegExp("[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)|(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))",e?void 0:"g")},532:(e,t,r)=>{let n=r(511);e.exports=e=>"string"==typeof e?e.replace(n(),""):e}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var s=r[e]={exports:{}},i=!0;try{t[e](s,s.exports,n),i=!1}finally{i&&delete r[e]}return s.exports}n.ab=__dirname+"/";var o=n(532);e.exports=o})()},"./dist/esm/build/output/log.js":(e,t,r)=>{"use strict";var n;r.d(t,{ZK:()=>v});let{env:o,stdout:s}=(null==(n=globalThis)?void 0:n.process)??{},i=o&&!o.NO_COLOR&&(o.FORCE_COLOR||(null==s?void 0:s.isTTY)&&!o.CI&&"dumb"!==o.TERM),a=(e,t,r,n)=>{let o=e.substring(0,n)+r,s=e.substring(n+t.length),i=s.indexOf(t);return~i?o+a(s,t,r,i):o+s},l=(e,t,r=e)=>i?n=>{let o=""+n,s=o.indexOf(t,e.length);return~s?e+a(o,t,r,s)+t:e+o+t}:String,d=l("\x1b[1m","\x1b[22m","\x1b[22m\x1b[1m");l("\x1b[2m","\x1b[22m","\x1b[22m\x1b[2m"),l("\x1b[3m","\x1b[23m"),l("\x1b[4m","\x1b[24m"),l("\x1b[7m","\x1b[27m"),l("\x1b[8m","\x1b[28m"),l("\x1b[9m","\x1b[29m"),l("\x1b[30m","\x1b[39m");let u=l("\x1b[31m","\x1b[39m"),c=l("\x1b[32m","\x1b[39m"),p=l("\x1b[33m","\x1b[39m");l("\x1b[34m","\x1b[39m");let h=l("\x1b[35m","\x1b[39m");l("\x1b[38;2;173;127;168m","\x1b[39m"),l("\x1b[36m","\x1b[39m");let f=l("\x1b[37m","\x1b[39m");l("\x1b[90m","\x1b[39m"),l("\x1b[40m","\x1b[49m"),l("\x1b[41m","\x1b[49m"),l("\x1b[42m","\x1b[49m"),l("\x1b[43m","\x1b[49m"),l("\x1b[44m","\x1b[49m"),l("\x1b[45m","\x1b[49m"),l("\x1b[46m","\x1b[49m"),l("\x1b[47m","\x1b[49m");let m={wait:f(d("○")),error:u(d("⨯")),warn:p(d("⚠")),ready:"▲",info:f(d(" ")),event:c(d("✓")),trace:h(d("\xbb"))},g={log:"log",warn:"warn",error:"error"};function v(...e){!function(e,...t){(""===t[0]||void 0===t[0])&&1===t.length&&t.shift();let r=e in g?g[e]:"log",n=m[e];0===t.length?console[r](""):console[r](" "+n,...t)}("warn",...e)}},"./dist/esm/lib/constants.js":(e,t,r)=>{"use strict";r.d(t,{BR:()=>s,Ei:()=>d,Eo:()=>p,Lx:()=>c,Qq:()=>o,Wo:()=>a,lk:()=>h,oL:()=>l,q6:()=>u,wh:()=>i,y3:()=>n});let n="x-prerender-revalidate",o="x-prerender-revalidate-if-generated",s=31536e3,i="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",a="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",l="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",d="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",u="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",c="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",p="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",h="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",f={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"};({...f,GROUP:{server:[f.reactServerComponents,f.actionBrowser,f.appMetadataRoute,f.appRouteHandler],nonClientServerTarget:[f.middleware,f.api],app:[f.reactServerComponents,f.actionBrowser,f.appMetadataRoute,f.appRouteHandler,f.serverSideRendering,f.appPagesBrowser,f.shared]}})},"./dist/esm/server/api-utils/index.js":(e,t,r)=>{"use strict";r.d(t,{Di:()=>l,Iq:()=>s,Lm:()=>u,QM:()=>a,dS:()=>i,gk:()=>c});var n=r("./dist/esm/server/web/spec-extension/adapters/headers.js"),o=r("./dist/esm/lib/constants.js");function s(e,t){let r=n.h.from(e.headers),s=r.get(o.y3),i=s===t.previewModeId,a=r.has(o.Qq);return{isOnDemandRevalidate:i,revalidateOnlyGenerated:a}}let i="__prerender_bypass",a="__next_preview_data",l=Symbol(a),d=Symbol(i);function u(e,t={}){if(d in e)return e;let{serialize:n}=r("./dist/compiled/cookie/index.js"),o=e.getHeader("Set-Cookie");return e.setHeader("Set-Cookie",[..."string"==typeof o?[o]:Array.isArray(o)?o:[],n(i,"",{expires:new Date(0),httpOnly:!0,sameSite:"none",secure:!0,path:"/",...void 0!==t.path?{path:t.path}:void 0}),n(a,"",{expires:new Date(0),httpOnly:!0,sameSite:"none",secure:!0,path:"/",...void 0!==t.path?{path:t.path}:void 0})]),Object.defineProperty(e,d,{value:!0,enumerable:!1}),e}function c({req:e},t,r){let n={configurable:!0,enumerable:!0},o={...n,writable:!0};Object.defineProperty(e,t,{...n,get:()=>{let n=r();return Object.defineProperty(e,t,{...o,value:n}),n},set:r=>{Object.defineProperty(e,t,{...o,value:r})}})}},"./dist/esm/server/api-utils/node/try-get-preview-data.js":(e,t,r)=>{"use strict";r.d(t,{R:()=>i});var n=r("./dist/esm/server/api-utils/index.js"),o=r("./dist/esm/server/web/spec-extension/cookies.js"),s=r("./dist/esm/server/web/spec-extension/adapters/headers.js");function i(e,t,i){var a,l;let d;if(i&&(0,n.Iq)(e,i).isOnDemandRevalidate)return!1;if(n.Di in e)return e[n.Di];let u=s.h.from(e.headers),c=new o.q(u),p=null==(a=c.get(n.dS))?void 0:a.value,h=null==(l=c.get(n.QM))?void 0:l.value;if(p&&!h&&p===i.previewModeId){let t={};return Object.defineProperty(e,n.Di,{value:t,enumerable:!1}),t}if(!p&&!h)return!1;if(!p||!h||p!==i.previewModeId)return(0,n.Lm)(t),!1;try{let e=r("next/dist/compiled/jsonwebtoken");d=e.verify(h,i.previewModeSigningKey)}catch{return(0,n.Lm)(t),!1}let{decryptWithSecret:f}=r("./dist/esm/server/crypto-utils.js"),m=f(Buffer.from(i.previewModeEncryptionKey),d.data);try{let t=JSON.parse(m);return Object.defineProperty(e,n.Di,{value:t,enumerable:!1}),t}catch{return!1}}},"./dist/esm/server/crypto-utils.js":(e,t,r)=>{"use strict";r.r(t),r.d(t,{decryptWithSecret:()=>a,encryptWithSecret:()=>i});let n=require("crypto");var o=r.n(n);let s="aes-256-gcm";function i(e,t){let r=o().randomBytes(16),n=o().randomBytes(64),i=o().pbkdf2Sync(e,n,1e5,32,"sha512"),a=o().createCipheriv(s,i,r),l=Buffer.concat([a.update(t,"utf8"),a.final()]),d=a.getAuthTag();return Buffer.concat([n,r,d,l]).toString("hex")}function a(e,t){let r=Buffer.from(t,"hex"),n=r.slice(0,64),i=r.slice(64,80),a=r.slice(80,96),l=r.slice(96),d=o().pbkdf2Sync(e,n,1e5,32,"sha512"),u=o().createDecipheriv(s,d,i);return u.setAuthTag(a),u.update(l)+u.final("utf8")}},"./dist/esm/server/optimize-amp.js":(e,t,r)=>{"use strict";async function n(e,t){let n;try{n=r("next/dist/compiled/@ampproject/toolbox-optimizer")}catch(t){return e}let o=n.create(t);return o.transformHtml(e,t)}r.d(t,{Z:()=>n})},"./dist/esm/server/post-process.js":(e,t,r)=>{"use strict";r.d(t,{X:()=>l});var n,o=r("./dist/esm/shared/lib/constants.js");function s(e){return null!=e}let i=[];async function a(e,t,n){if(!i[0])return e;let{parse:o}=r("next/dist/compiled/node-html-parser"),s=o(e),a=e;async function l(e){let r=e.inspect(s,t);a=await e.mutate(a,r,t)}for(let e=0;e<i.length;e++){let t=i[e];(!t.condition||t.condition(n))&&await l(i[e].middleware)}return a}async function l(e,t,n,{inAmpMode:o,hybridAmp:i}){let l=[o?async t=>{let o=r("./dist/esm/server/optimize-amp.js").Z;return t=await o(t,n.ampOptimizerConfig),!n.ampSkipValidation&&n.ampValidator&&await n.ampValidator(t,e),t}:null,(0,n.optimizeFonts)?async e=>await a(e,{getFontDefinition:e=>{var t;return n.fontManifest&&(null==(t=n.fontManifest.find(t=>!!t&&t.url===e))?void 0:t.content)||""}},{optimizeFonts:n.optimizeFonts}):null,(0,n.optimizeCss)?async e=>{let t=r("critters"),o=new t({ssrMode:!0,reduceInlineStyles:!1,path:n.distDir,publicPath:`${n.assetPrefix}/_next/`,preload:"media",fonts:!1,...n.optimizeCss});return await o.process(e)}:null,o||i?e=>e.replace(/&amp;amp=1/g,"&amp=1"):null].filter(s);for(let e of l)e&&(t=await e(t));return t}n=new class{inspect(e,t){if(!t.getFontDefinition)return;let r=[];return e.querySelectorAll("link").filter(e=>"stylesheet"===e.getAttribute("rel")&&e.hasAttribute("data-href")&&o.C7.some(({url:t})=>{let r=e.getAttribute("data-href");return!!r&&r.startsWith(t)})).forEach(e=>{let t=e.getAttribute("data-href"),n=e.getAttribute("nonce");t&&r.push([t,n])}),r}constructor(){this.mutate=async(e,t,r)=>{let n=e,s=new Set;if(!r.getFontDefinition)return e;t.forEach(e=>{let[t,i]=e,a=`<link rel="stylesheet" href="${t}"/>`;if(n.indexOf(`<style data-href="${t}">`)>-1||n.indexOf(a)>-1)return;let l=r.getFontDefinition?r.getFontDefinition(t):null;if(l){let e=i?` nonce="${i}"`:"",r="";l.includes("ascent-override")&&(r=' data-size-adjust="true"'),n=n.replace("</head>",`<style data-href="${t}"${e}${r}>${l}</style></head>`);let a=t.replace(/&/g,"&amp;").replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),d=RegExp(`<link[^>]*data-href="${a}"[^>]*/>`);n=n.replace(d,"");let u=o.C7.find(e=>t.startsWith(e.url));u&&s.add(u.preconnect)}else n=n.replace("</head>",`${a}</head>`)});let i="";return s.forEach(e=>{i+=`<link rel="preconnect" href="${e}" crossorigin />`}),n=n.replace('<meta name="next-font-preconnect"/>',i)}}},i.push({name:"Inline-Fonts",middleware:n,condition:(e=>e.optimizeFonts||process.env.__NEXT_OPTIMIZE_FONTS)||null})},"./dist/esm/server/web/spec-extension/adapters/headers.js":(e,t,r)=>{"use strict";r.d(t,{h:()=>s});var n=r("./dist/esm/server/web/spec-extension/adapters/reflect.js");class o extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new o}}class s extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,o){if("symbol"==typeof r)return n.g.get(t,r,o);let s=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===s);if(void 0!==i)return n.g.get(t,i,o)},set(t,r,o,s){if("symbol"==typeof r)return n.g.set(t,r,o,s);let i=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===i);return n.g.set(t,a??r,o,s)},has(t,r){if("symbol"==typeof r)return n.g.has(t,r);let o=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0!==s&&n.g.has(t,s)},deleteProperty(t,r){if("symbol"==typeof r)return n.g.deleteProperty(t,r);let o=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0===s||n.g.deleteProperty(t,s)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return o.callable;default:return n.g.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new s(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},"./dist/esm/server/web/spec-extension/adapters/reflect.js":(e,t,r)=>{"use strict";r.d(t,{g:()=>n});class n{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},"./dist/esm/server/web/spec-extension/cookies.js":(e,t,r)=>{"use strict";r.d(t,{q:()=>n.RequestCookies});var n=r("./dist/compiled/@edge-runtime/cookies/index.js")},"./dist/esm/shared/lib/constants.js":(e,t,r)=>{"use strict";r.d(t,{C7:()=>a,Er:()=>l,NO:()=>s,uY:()=>i,wU:()=>o}),r("./dist/esm/shared/lib/modern-browserslist-target.js");let n={client:"client",server:"server",edgeServer:"edge-server"};n.client,n.server,n.edgeServer;let o="__NEXT_BUILTIN_DOCUMENT__";Symbol("polyfills");let s="__N_SSG",i="__N_SSP",a=[{url:"https://fonts.googleapis.com/",preconnect:"https://fonts.gstatic.com"},{url:"https://use.typekit.net",preconnect:"https://use.typekit.net"}],l=["/500"]},"./dist/esm/shared/lib/modern-browserslist-target.js":e=>{e.exports=["chrome 64","edge 79","firefox 67","opera 51","safari 12"]},critters:e=>{"use strict";e.exports=require("critters")},"next/dist/compiled/@ampproject/toolbox-optimizer":e=>{"use strict";e.exports=require("next/dist/compiled/@ampproject/toolbox-optimizer")},"next/dist/compiled/@next/react-dev-overlay/dist/middleware":e=>{"use strict";e.exports=require("next/dist/compiled/@next/react-dev-overlay/dist/middleware")},"next/dist/compiled/jsonwebtoken":e=>{"use strict";e.exports=require("next/dist/compiled/jsonwebtoken")},"next/dist/compiled/node-html-parser":e=>{"use strict";e.exports=require("next/dist/compiled/node-html-parser")},path:e=>{"use strict";e.exports=require("path")}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var s=t[n]={exports:{}};return e[n](s,s.exports,r),s.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};(()=>{"use strict";let e,t,o;r.r(n),r.d(n,{PagesRouteModule:()=>tt,default:()=>tn,renderToHTML:()=>e5,vendored:()=>tr});var s,i,a,l,d,u,c,p,h,f,m,g,v={};r.r(v),r.d(v,{AmpStateContext:()=>B});var y={};r.r(y),r.d(y,{HeadManagerContext:()=>U});var x={};r.r(x),r.d(x,{LoadableContext:()=>W});var b={};r.r(b),r.d(b,{default:()=>X});var w={};r.r(w),r.d(w,{RouterContext:()=>K});var S={};r.r(S),r.d(S,{HtmlContext:()=>ed,useHtmlContext:()=>eu});var P={};r.r(P),r.d(P,{ImageConfigContext:()=>eM});var _={};r.r(_),r.d(_,{PathParamsContext:()=>eB,PathnameContext:()=>ez,SearchParamsContext:()=>eH});var j={};r.r(j),r.d(j,{AppRouterContext:()=>eQ,GlobalLayoutRouterContext:()=>eY,LayoutRouterContext:()=>eZ,MissingSlotContext:()=>eK,TemplateContext:()=>eX});var C={};r.r(C),r.d(C,{ServerInsertedHTMLContext:()=>e7,useServerInsertedHTML:()=>te});var R={};r.r(R),r.d(R,{AmpContext:()=>v,AppRouterContext:()=>j,HeadManagerContext:()=>y,HooksClientContext:()=>_,HtmlContext:()=>S,ImageConfigContext:()=>P,Loadable:()=>b,LoadableContext:()=>x,RouterContext:()=>w,ServerInsertedHtml:()=>C});class T{constructor({userland:e,definition:t}){this.userland=e,this.definition=t}}let E=require("react/jsx-runtime");var $=r("./dist/esm/server/api-utils/index.js");let A=require("react");var L=r.n(A);let N=require("react-dom/server.browser");var O=r.n(N);let I=require("styled-jsx");var k=r("./dist/esm/lib/constants.js"),M=r("./dist/esm/shared/lib/constants.js");function q(e){return Object.prototype.toString.call(e)}function D(e){if("[object Object]"!==q(e))return!1;let t=Object.getPrototypeOf(e);return null===t||t.hasOwnProperty("isPrototypeOf")}let F=/^[A-Za-z_$][A-Za-z0-9_$]*$/;class H extends Error{constructor(e,t,r,n){super(r?`Error serializing \`${r}\` returned from \`${t}\` in "${e}".
Reason: ${n}`:`Error serializing props returned from \`${t}\` in "${e}".
Reason: ${n}`)}}function z(e,t,r){if(!D(r))throw new H(e,t,"",`Props must be returned as a plain object from ${t}: \`{ props: { ... } }\` (received: \`${q(r)}\`).`);function n(r,n,o){if(r.has(n))throw new H(e,t,o,`Circular references cannot be expressed in JSON (references: \`${r.get(n)||"(self)"}\`).`);r.set(n,o)}return function r(o,s,i){let a=typeof s;if(null===s||"boolean"===a||"number"===a||"string"===a)return!0;if("undefined"===a)throw new H(e,t,i,"`undefined` cannot be serialized as JSON. Please use `null` or omit this value.");if(D(s)){if(n(o,s,i),Object.entries(s).every(([e,t])=>{let n=F.test(e)?`${i}.${e}`:`${i}[${JSON.stringify(e)}]`,s=new Map(o);return r(s,e,n)&&r(s,t,n)}))return!0;throw new H(e,t,i,"invariant: Unknown error encountered in Object.")}if(Array.isArray(s)){if(n(o,s,i),s.every((e,t)=>{let n=new Map(o);return r(n,e,`${i}[${t}]`)}))return!0;throw new H(e,t,i,"invariant: Unknown error encountered in Array.")}throw new H(e,t,i,"`"+a+"`"+("object"===a?` ("${Object.prototype.toString.call(s)}")`:"")+" cannot be serialized as JSON. Please only return JSON serializable data types.")}(new Map,r,"")}let B=L().createContext({}),U=L().createContext({}),W=L().createContext(null),J=[],G=[];function V(e){let t=e(),r={loading:!0,loaded:null,error:null};return r.promise=t.then(e=>(r.loading=!1,r.loaded=e,e)).catch(e=>{throw r.loading=!1,r.error=e,e}),r}class Q{promise(){return this._res.promise}retry(){this._clearTimeouts(),this._res=this._loadFn(this._opts.loader),this._state={pastDelay:!1,timedOut:!1};let{_res:e,_opts:t}=this;e.loading&&("number"==typeof t.delay&&(0===t.delay?this._state.pastDelay=!0:this._delay=setTimeout(()=>{this._update({pastDelay:!0})},t.delay)),"number"==typeof t.timeout&&(this._timeout=setTimeout(()=>{this._update({timedOut:!0})},t.timeout))),this._res.promise.then(()=>{this._update({}),this._clearTimeouts()}).catch(e=>{this._update({}),this._clearTimeouts()}),this._update({})}_update(e){this._state={...this._state,error:this._res.error,loaded:this._res.loaded,loading:this._res.loading,...e},this._callbacks.forEach(e=>e())}_clearTimeouts(){clearTimeout(this._delay),clearTimeout(this._timeout)}getCurrentValue(){return this._state}subscribe(e){return this._callbacks.add(e),()=>{this._callbacks.delete(e)}}constructor(e,t){this._loadFn=e,this._opts=t,this._callbacks=new Set,this._delay=null,this._timeout=null,this.retry()}}function Z(e){return function(e,t){let r=Object.assign({loader:null,loading:null,delay:200,timeout:null,webpack:null,modules:null},t),n=null;function o(){if(!n){let t=new Q(e,r);n={getCurrentValue:t.getCurrentValue.bind(t),subscribe:t.subscribe.bind(t),retry:t.retry.bind(t),promise:t.promise.bind(t)}}return n.promise()}function s(e,t){!function(){o();let e=L().useContext(W);e&&Array.isArray(r.modules)&&r.modules.forEach(t=>{e(t)})}();let s=L().useSyncExternalStore(n.subscribe,n.getCurrentValue,n.getCurrentValue);return L().useImperativeHandle(t,()=>({retry:n.retry}),[]),L().useMemo(()=>{var t;return s.loading||s.error?L().createElement(r.loading,{isLoading:s.loading,pastDelay:s.pastDelay,timedOut:s.timedOut,error:s.error,retry:n.retry}):s.loaded?L().createElement((t=s.loaded)&&t.default?t.default:t,e):null},[e,s])}return J.push(o),s.preload=()=>o(),s.displayName="LoadableComponent",L().forwardRef(s)}(V,e)}function Y(e,t){let r=[];for(;e.length;){let n=e.pop();r.push(n(t))}return Promise.all(r).then(()=>{if(e.length)return Y(e,t)})}Z.preloadAll=()=>new Promise((e,t)=>{Y(J).then(e,t)}),Z.preloadReady=e=>(void 0===e&&(e=[]),new Promise(t=>{let r=()=>t();Y(G,e).then(r,r)}));let X=Z,K=L().createContext(null);function ee(e){return e.startsWith("/")?e:"/"+e}let et=["(..)(..)","(.)","(..)","(...)"],er=/\/\[[^/]+?\](?=\/|$)/;function en(e){return void 0!==e.split("/").find(e=>et.find(t=>e.startsWith(t)))&&(e=function(e){let t,r,n;for(let o of e.split("/"))if(r=et.find(e=>o.startsWith(e))){[t,n]=e.split(r,2);break}if(!t||!r||!n)throw Error(`Invalid interception route: ${e}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(t=ee(t.split("/").reduce((e,t,r,n)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t:e,"")),r){case"(.)":n="/"===t?`/${n}`:t+"/"+n;break;case"(..)":if("/"===t)throw Error(`Invalid interception route: ${e}. Cannot use (..) marker at the root level, use (.) instead.`);n=t.split("/").slice(0,-1).concat(n).join("/");break;case"(...)":n="/"+n;break;case"(..)(..)":let o=t.split("/");if(o.length<=2)throw Error(`Invalid interception route: ${e}. Cannot use (..)(..) marker at the root level or one level up.`);n=o.slice(0,-2).concat(n).join("/");break;default:throw Error("Invariant: unexpected marker")}return{interceptingRoute:t,interceptedRoute:n}}(e).interceptedRoute),er.test(e)}function eo(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function es(e){return e.finished||e.headersSent}async function ei(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await ei(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&es(r))return n;if(!n){let t='"'+eo(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.';throw Error(t)}return n}let ea="undefined"!=typeof performance;ea&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class el extends Error{}let ed=(0,A.createContext)(void 0);function eu(){let e=(0,A.useContext)(ed);if(!e)throw Error("<Html> should not be imported outside of pages/_document.\nRead more: https://nextjs.org/docs/messages/no-document-import-in-page");return e}let ec=Symbol.for("NextInternalRequestMeta");function ep(e,t){let r=e[ec]||{};return"string"==typeof t?r[t]:r}!function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(s||(s={}));let eh=new Set([301,302,303,307,308]);function ef(e){return e.statusCode||(e.permanent?s.PermanentRedirect:s.TemporaryRedirect)}let em=require("next/dist/server/lib/trace/tracer");(function(e){e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404"})(i||(i={})),function(e){e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents"}(a||(a={})),function(e){e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer"}(l||(l={})),function(e){e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch"}(d||(d={})),(u||(u={})).startServer="startServer.startServer",function(e){e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult"}(c||(c={})),function(e){e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch"}(p||(p={})),(h||(h={})).executeRoute="Router.executeRoute",(f||(f={})).runHandler="Node.runHandler",(m||(m={})).runHandler="AppRouteRouteHandlers.runHandler",function(e){e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport"}(g||(g={}));class eg{constructor(){let e,t;this.promise=new Promise((r,n)=>{e=r,t=n}),this.resolve=e,this.reject=t}}let ev=e=>{setImmediate(e)};function ey(...e){let{readable:t,writable:r}=new TransformStream,n=Promise.resolve();for(let t=0;t<e.length;++t)n=n.then(()=>e[t].pipeTo(r,{preventClose:t+1<e.length}));return n.catch(()=>{}),t}function ex(e){let t=new TextEncoder;return new ReadableStream({start(r){r.enqueue(t.encode(e)),r.close()}})}async function eb(e){let t="";return await e.pipeThrough(function(e=new TextDecoder){return new TransformStream({transform:(t,r)=>r.enqueue(e.decode(t,{stream:!0})),flush:t=>t.enqueue(e.decode())})}()).pipeTo(new WritableStream({write(e){t+=e}})),t}async function ew(e,{suffix:t,inlinedDataStream:r,isStaticGeneration:n,getServerInsertedHTML:o,serverInsertedHTMLToHead:s,validateRootLayout:i}){let a="</body></html>",l=t?t.split(a,1)[0]:null;return n&&"allReady"in e&&await e.allReady,function(e,t){let r=e;for(let e of t)e&&(r=r.pipeThrough(e));return r}(e,[function(){let e,t=new Uint8Array,r=r=>{if(e)return;let n=new eg;e=n,ev(()=>{try{r.enqueue(t),t=new Uint8Array}catch{}finally{e=void 0,n.resolve()}})};return new TransformStream({transform(e,n){let o=new Uint8Array(t.length+e.byteLength);o.set(t),o.set(e,t.length),t=o,r(n)},flush(){if(e)return e.promise}})}(),o&&!s?function(e){let t=new TextEncoder;return new TransformStream({transform:async(r,n)=>{let o=await e();o&&n.enqueue(t.encode(o)),n.enqueue(r)}})}(o):null,null!=l&&l.length>0?function(e){let t,r=!1,n=new TextEncoder,o=r=>{let o=new eg;t=o,ev(()=>{try{r.enqueue(n.encode(e))}catch{}finally{t=void 0,o.resolve()}})};return new TransformStream({transform(e,t){t.enqueue(e),r||(r=!0,o(t))},flush(o){if(t)return t.promise;r||o.enqueue(n.encode(e))}})}(l):null,r?function(e){let t=!1,r=null,n=t=>{let n=e.getReader(),o=new eg;r=o,ev(async()=>{try{for(;;){let{done:e,value:r}=await n.read();if(e)return;t.enqueue(r)}}catch(e){t.error(e)}finally{o.resolve()}})};return new TransformStream({transform(e,r){r.enqueue(e),t||(t=!0,n(r))},flush(){if(r&&t)return r.promise}})}(r):null,function(e){let t=!1,r=new TextEncoder,n=new TextDecoder;return new TransformStream({transform(o,s){if(t)return s.enqueue(o);let i=n.decode(o),a=i.indexOf(e);if(a>-1){if(t=!0,i.length===e.length)return;let n=i.slice(0,a);if(o=r.encode(n),s.enqueue(o),i.length>e.length+a){let t=i.slice(a+e.length);o=r.encode(t),s.enqueue(o)}}else s.enqueue(o)},flush(t){t.enqueue(r.encode(e))}})}(a),o&&s?function(e){let t=!1,r=!1,n=new TextEncoder,o=new TextDecoder;return new TransformStream({async transform(s,i){if(r){i.enqueue(s);return}let a=await e();if(t)i.enqueue(n.encode(a)),i.enqueue(s),r=!0;else{let e=o.decode(s),l=e.indexOf("</head>");if(-1!==l){let o=e.slice(0,l)+a+e.slice(l);i.enqueue(n.encode(o)),r=!0,t=!0}}t?ev(()=>{r=!1}):i.enqueue(s)},async flush(t){let r=await e();r&&t.enqueue(n.encode(r))}})}(o):null,i?function(e="",t){let r=!1,n=!1,o=new TextEncoder,s=new TextDecoder,i="";return new TransformStream({async transform(e,t){(!r||!n)&&(i+=s.decode(e,{stream:!0}),!r&&i.includes("<html")&&(r=!0),!n&&i.includes("<body")&&(n=!0)),t.enqueue(e)},flush(a){(!r||!n)&&(i+=s.decode(),!r&&i.includes("<html")&&(r=!0),!n&&i.includes("<body")&&(n=!0));let l=[];r||l.push("html"),n||l.push("body"),l.length>0&&a.enqueue(o.encode(`<script>self.__next_root_layout_missing_tags_error=${JSON.stringify({missingTags:l,assetPrefix:e??"",tree:t()})}</script>`))}})}(i.assetPrefix,i.getTree):null])}function eS(e){return e.replace(/\/$/,"")||"/"}function eP(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function e_(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:o}=eP(e);return""+t+r+n+o}function ej(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:o}=eP(e);return""+r+t+n+o}function eC(e,t){if("string"!=typeof e)return!1;let{pathname:r}=eP(e);return r===t||r.startsWith(t+"/")}function eR(e,t){let r;let n=e.split("/");return(t||[]).some(t=>!!n[1]&&n[1].toLowerCase()===t.toLowerCase()&&(r=t,n.splice(1,1),e=n.join("/")||"/",!0)),{pathname:e,detectedLocale:r}}let eT=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function eE(e,t){return new URL(String(e).replace(eT,"localhost"),t&&String(t).replace(eT,"localhost"))}let e$=Symbol("NextURLInternal");class eA{constructor(e,t,r){let n,o;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,o=r||{}):o=r||t||{},this[e$]={url:eE(e,n??o.base),options:o,basePath:""},this.analyze()}analyze(){var e,t,r,n,o;let s=function(e,t){var r,n;let{basePath:o,i18n:s,trailingSlash:i}=null!=(r=t.nextConfig)?r:{},a={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):i};o&&eC(a.pathname,o)&&(a.pathname=function(e,t){if(!eC(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(a.pathname,o),a.basePath=o);let l=a.pathname;if(a.pathname.startsWith("/_next/data/")&&a.pathname.endsWith(".json")){let e=a.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),r=e[0];a.buildId=r,l="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(a.pathname=l)}if(s){let e=t.i18nProvider?t.i18nProvider.analyze(a.pathname):eR(a.pathname,s.locales);a.locale=e.detectedLocale,a.pathname=null!=(n=e.pathname)?n:a.pathname,!e.detectedLocale&&a.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(l):eR(l,s.locales)).detectedLocale&&(a.locale=e.detectedLocale)}return a}(this[e$].url.pathname,{nextConfig:this[e$].options.nextConfig,parseData:!process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE,i18nProvider:this[e$].options.i18nProvider}),i=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[e$].url,this[e$].options.headers);this[e$].domainLocale=this[e$].options.i18nProvider?this[e$].options.i18nProvider.detectDomainLocale(i):function(e,t,r){if(e)for(let s of(r&&(r=r.toLowerCase()),e)){var n,o;let e=null==(n=s.domain)?void 0:n.split(":",1)[0].toLowerCase();if(t===e||r===s.defaultLocale.toLowerCase()||(null==(o=s.locales)?void 0:o.some(e=>e.toLowerCase()===r)))return s}}(null==(t=this[e$].options.nextConfig)?void 0:null==(e=t.i18n)?void 0:e.domains,i);let a=(null==(r=this[e$].domainLocale)?void 0:r.defaultLocale)||(null==(o=this[e$].options.nextConfig)?void 0:null==(n=o.i18n)?void 0:n.defaultLocale);this[e$].url.pathname=s.pathname,this[e$].defaultLocale=a,this[e$].basePath=s.basePath??"",this[e$].buildId=s.buildId,this[e$].locale=s.locale??a,this[e$].trailingSlash=s.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,n){if(!t||t===r)return e;let o=e.toLowerCase();return!n&&(eC(o,"/api")||eC(o,"/"+t.toLowerCase()))?e:e_(e,"/"+t)}((e={basePath:this[e$].basePath,buildId:this[e$].buildId,defaultLocale:this[e$].options.forceLocale?void 0:this[e$].defaultLocale,locale:this[e$].locale,pathname:this[e$].url.pathname,trailingSlash:this[e$].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=eS(t)),e.buildId&&(t=ej(e_(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=e_(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:ej(t,"/"):eS(t)}formatSearch(){return this[e$].url.search}get buildId(){return this[e$].buildId}set buildId(e){this[e$].buildId=e}get locale(){return this[e$].locale??""}set locale(e){var t,r;if(!this[e$].locale||!(null==(r=this[e$].options.nextConfig)?void 0:null==(t=r.i18n)?void 0:t.locales.includes(e)))throw TypeError(`The NextURL configuration includes no locale "${e}"`);this[e$].locale=e}get defaultLocale(){return this[e$].defaultLocale}get domainLocale(){return this[e$].domainLocale}get searchParams(){return this[e$].url.searchParams}get host(){return this[e$].url.host}set host(e){this[e$].url.host=e}get hostname(){return this[e$].url.hostname}set hostname(e){this[e$].url.hostname=e}get port(){return this[e$].url.port}set port(e){this[e$].url.port=e}get protocol(){return this[e$].url.protocol}set protocol(e){this[e$].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[e$].url=eE(e),this.analyze()}get origin(){return this[e$].url.origin}get pathname(){return this[e$].url.pathname}set pathname(e){this[e$].url.pathname=e}get hash(){return this[e$].url.hash}set hash(e){this[e$].url.hash=e}get search(){return this[e$].url.search}set search(e){this[e$].url.search=e}get password(){return this[e$].url.password}set password(e){this[e$].url.password=e}get username(){return this[e$].url.username}set username(e){this[e$].url.username=e}get basePath(){return this[e$].basePath}set basePath(e){this[e$].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new eA(String(this),this[e$].options)}}r("./dist/esm/server/web/spec-extension/cookies.js"),Symbol("internal request"),Request,Symbol.for("edge-runtime.inspect.custom");let eL="ResponseAborted";class eN extends Error{constructor(...e){super(...e),this.name=eL}}function eO(e){return(null==e?void 0:e.name)==="AbortError"||(null==e?void 0:e.name)===eL}async function eI(e,t,r){try{let{errored:n,destroyed:o}=t;if(n||o)return;let s=function(e){let t=new AbortController;return e.once("close",()=>{e.writableFinished||t.abort(new eN)}),t}(t),i=function(e,t){let r=!1,n=new eg;function o(){n.resolve()}e.on("drain",o),e.once("close",()=>{e.off("drain",o),n.resolve()});let s=new eg;return e.once("finish",()=>{s.resolve()}),new WritableStream({write:async t=>{r||(r=!0,e.flushHeaders());try{let r=e.write(t);"flush"in e&&"function"==typeof e.flush&&e.flush(),r||(await n.promise,n=new eg)}catch(t){throw e.end(),Error("failed to write chunk to response",{cause:t})}},abort:t=>{e.writableFinished||e.destroy(t)},close:async()=>{if(t&&await t,!e.writableFinished)return e.end(),s.promise}})}(t,r);await e.pipeTo(i,{signal:s.signal})}catch(e){if(eO(e))return;throw Error("failed to pipe response",{cause:e})}}class ek{static fromStatic(e){return new ek(e,{metadata:{}})}constructor(e,{contentType:t,waitUntil:r,metadata:n}){this.response=e,this.contentType=t,this.metadata=n,this.waitUntil=r}assignMetadata(e){Object.assign(this.metadata,e)}get isNull(){return null===this.response}get isDynamic(){return"string"!=typeof this.response}toUnchunkedString(e=!1){if(null===this.response)throw Error("Invariant: null responses cannot be unchunked");if("string"!=typeof this.response){if(!e)throw Error("Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js");return eb(this.readable)}return this.response}get readable(){if(null===this.response)throw Error("Invariant: null responses cannot be streamed");if("string"==typeof this.response)throw Error("Invariant: static responses cannot be streamed");return Array.isArray(this.response)?ey(...this.response):this.response}chain(e){let t;if(null===this.response)throw Error("Invariant: response is null. This is a bug in Next.js");(t="string"==typeof this.response?[ex(this.response)]:Array.isArray(this.response)?this.response:[this.response]).push(e),this.response=t}async pipeTo(e){try{await this.readable.pipeTo(e,{preventClose:!0}),this.waitUntil&&await this.waitUntil,await e.close()}catch(t){if(eO(t)){await e.abort(t);return}throw t}}async pipeToNodeResponse(e){await eI(this.readable,e,this.waitUntil)}}let eM=L().createContext({deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[],unoptimized:!1});var eq=r("./dist/compiled/strip-ansi/index.js"),eD=r.n(eq);let eF=["__nextFallback","__nextLocale","__nextInferredLocaleFromDefault","__nextDefaultLocale","__nextIsNotFound","_rsc"],eH=(0,A.createContext)(null),ez=(0,A.createContext)(null),eB=(0,A.createContext)(null),eU=/[|\\{}()[\]^$+*?.-]/,eW=/[|\\{}()[\]^$+*?.-]/g;function eJ(e){return eU.test(e)?e.replace(eW,"\\$&"):e}function eG(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function eV(e){let{children:t,router:r,...n}=e,o=(0,A.useRef)(n.isAutoExport),s=(0,A.useMemo)(()=>{let e;let t=o.current;if(t&&(o.current=!1),en(r.pathname)&&(r.isFallback||t&&!r.isReady))return null;try{e=new URL(r.asPath,"http://f")}catch(e){return"/"}return e.pathname},[r.asPath,r.isFallback,r.isReady,r.pathname]);return(0,E.jsx)(ez.Provider,{value:s,children:t})}let eQ=L().createContext(null),eZ=L().createContext(null),eY=L().createContext(null),eX=L().createContext(null),eK=L().createContext(new Set),e0="<!DOCTYPE html>";function e1(){throw Error('No router instance found. you should only use "next/router" inside the client side of your app. https://nextjs.org/docs/messages/no-router-instance')}async function e4(e){let t=await O().renderToReadableStream(e);return await t.allReady,eb(t)}e=r("./dist/esm/server/api-utils/node/try-get-preview-data.js").R,t=r("./dist/esm/build/output/log.js").ZK,o=r("./dist/esm/server/post-process.js").X;class e3{constructor(e,t,r,{isFallback:n},o,s,i,a,l,d,u,c){this.route=e.replace(/\/$/,"")||"/",this.pathname=e,this.query=t,this.asPath=r,this.isFallback=n,this.basePath=s,this.locale=i,this.locales=a,this.defaultLocale=l,this.isReady=o,this.domainLocales=d,this.isPreview=!!u,this.isLocaleDomain=!!c}push(){e1()}replace(){e1()}reload(){e1()}back(){e1()}forward(){e1()}prefetch(){e1()}beforePopState(){e1()}}function e2(e,t,r){return(0,E.jsx)(e,{Component:t,...r})}let e8=(e,t)=>{let r=`invalid-${e.toLocaleLowerCase()}-value`;return`Additional keys were returned from \`${e}\`. Properties intended for your component must be nested under the \`props\` key, e.g.:

	return { props: { title: 'My Title', content: '...' } }

Keys that need to be moved: ${t.join(", ")}.
Read more: https://nextjs.org/docs/messages/${r}`};function e9(e,t,r){let{destination:n,permanent:o,statusCode:s,basePath:i}=e,a=[],l=void 0!==s,d=void 0!==o;d&&l?a.push("`permanent` and `statusCode` can not both be provided"):d&&"boolean"!=typeof o?a.push("`permanent` must be `true` or `false`"):l&&!eh.has(s)&&a.push(`\`statusCode\` must undefined or one of ${[...eh].join(", ")}`);let u=typeof n;"string"!==u&&a.push(`\`destination\` should be string but received ${u}`);let c=typeof i;if("undefined"!==c&&"boolean"!==c&&a.push(`\`basePath\` should be undefined or a false, received ${c}`),a.length>0)throw Error(`Invalid redirect object returned from ${r} for ${t.url}
`+a.join(" and ")+"\nSee more info here: https://nextjs.org/docs/messages/invalid-redirect-gssp")}async function e6(n,s,i,a,l,d){var u,h;let f,m,g;(0,$.gk)({req:n},"cookies",(h=n.headers,function(){let{cookie:e}=h;if(!e)return{};let{parse:t}=r("./dist/compiled/cookie/index.js");return t(Array.isArray(e)?e.join("; "):e)}));let v={};if(v.assetQueryString=l.dev&&l.assetQueryString||"",l.dev&&!v.assetQueryString){let e=(n.headers["user-agent"]||"").toLowerCase();e.includes("safari")&&!e.includes("chrome")&&(v.assetQueryString=`?ts=${Date.now()}`)}l.deploymentId&&(v.assetQueryString+=`${v.assetQueryString?"&":"?"}dpl=${l.deploymentId}`),a=Object.assign({},a);let{err:y,dev:x=!1,ampPath:b="",pageConfig:w={},buildManifest:S,reactLoadableManifest:P,ErrorDebug:_,getStaticProps:j,getStaticPaths:C,getServerSideProps:R,isDataReq:T,params:A,previewProps:L,basePath:N,images:q,runtime:D,isExperimentalCompile:F}=l,{App:H}=d,J=v.assetQueryString,G=d.Document,V=l.Component,Q=!!a.__nextFallback,Z=a.__nextNotFoundSrcPage;!function(e){for(let t of eF)delete e[t]}(a);let Y=!!j,er=Y&&l.nextExport,ea=H.getInitialProps===H.origGetInitialProps,eu=!!(null==V?void 0:V.getInitialProps),ec=null==V?void 0:V.unstable_scriptLoader,eh=en(i),eg="/_error"===i&&V.getInitialProps===V.origGetInitialProps;l.nextExport&&eu&&!eg&&t(`Detected getInitialProps on page '${i}' while running export. It's recommended to use getStaticProps which has a more correct behavior for static exporting.
Read more: https://nextjs.org/docs/messages/get-initial-props-export`);let ev=!eu&&ea&&!Y&&!R;if(ev&&!x&&F&&(s.setHeader("Cache-Control","number"==typeof!1?"s-maxage=false, stale-while-revalidate":`s-maxage=${k.BR}, stale-while-revalidate`),ev=!1),eu&&Y)throw Error(k.wh+` ${i}`);if(eu&&R)throw Error(k.Wo+` ${i}`);if(R&&Y)throw Error(k.oL+` ${i}`);if(R&&"export"===l.nextConfigOutput)throw Error('getServerSideProps cannot be used with "output: export". See more info here: https://nextjs.org/docs/advanced-features/static-html-export');if(C&&!eh)throw Error(`getStaticPaths is only allowed for dynamic SSG pages and was found on '${i}'.
Read more: https://nextjs.org/docs/messages/non-dynamic-getstaticpaths-usage`);if(C&&!Y)throw Error(`getStaticPaths was added without a getStaticProps in ${i}. Without getStaticProps, getStaticPaths does nothing`);if(Y&&eh&&!C)throw Error(`getStaticPaths is required for dynamic SSG pages and is missing for '${i}'.
Read more: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`);let eP=l.resolvedAsPath||n.url;if(x){let{isValidElementType:e}=r("./dist/compiled/react-is/index.js");if(!e(V))throw Error(`The default export is not a React Component in page: "${i}"`);if(!e(H))throw Error('The default export is not a React Component in page: "/_app"');if(!e(G))throw Error('The default export is not a React Component in page: "/_document"');if((ev||Q)&&(a={...a.amp?{amp:a.amp}:{}},eP=`${i}${n.url.endsWith("/")&&"/"!==i&&!eh?"/":""}`,n.url=i),"/404"===i&&(eu||R))throw Error(`\`pages/404\` ${k.Ei}`);if(M.Er.includes(i)&&(eu||R))throw Error(`\`pages${i}\` ${k.Ei}`)}for(let e of["getStaticProps","getServerSideProps","getStaticPaths"])if(null==V?void 0:V[e])throw Error(`page ${i} ${e} ${k.lk}`);await X.preloadAll(),(Y||R)&&!Q&&L&&(g=!1!==(f=e(n,s,L)));let e_=!!(R||eu||!ea&&!Y||F),ej=new e3(i,a,eP,{isFallback:Q},e_,N,l.locale,l.locales,l.defaultLocale,l.domainLocales,g,ep(n,"isLocaleDomain")),eC={back(){ej.back()},forward(){ej.forward()},refresh(){ej.reload()},push(e,t){let{scroll:r}=void 0===t?{}:t;ej.push(e,void 0,{scroll:r})},replace(e,t){let{scroll:r}=void 0===t?{}:t;ej.replace(e,void 0,{scroll:r})},prefetch(e){ej.prefetch(e)}},eR={},eT=(0,I.createStyleRegistry)(),eE={ampFirst:!0===w.amp,hasQuery:!!a.amp,hybrid:"hybrid"===w.amp},e$=function(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}(eE),eA=function(e){void 0===e&&(e=!1);let t=[(0,E.jsx)("meta",{charSet:"utf-8"})];return e||t.push((0,E.jsx)("meta",{name:"viewport",content:"width=device-width"})),t}(e$),eL=[],eN={};ec&&(eN.beforeInteractive=[].concat(ec()).filter(e=>"beforeInteractive"===e.props.strategy).map(e=>e.props));let eO=({children:e})=>{var t;return(0,E.jsx)(eQ.Provider,{value:eC,children:(0,E.jsx)(eH.Provider,{value:ej.isReady&&ej.query?(t=ej.asPath,new URL(t,"http://n").searchParams):new URLSearchParams,children:(0,E.jsx)(eV,{router:ej,isAutoExport:ev,children:(0,E.jsx)(eB.Provider,{value:function(e){if(!e.isReady||!e.query)return null;let t={},r=function(e){let{parameterizedRoute:t,groups:r}=function(e){let t=eS(e).slice(1).split("/"),r={},n=1;return{parameterizedRoute:t.map(e=>{let t=et.find(t=>e.startsWith(t)),o=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&o){let{key:e,optional:s,repeat:i}=eG(o[1]);return r[e]={pos:n++,repeat:i,optional:s},"/"+eJ(t)+"([^/]+?)"}if(!o)return"/"+eJ(e);{let{key:e,repeat:t,optional:s}=eG(o[1]);return r[e]={pos:n++,repeat:t,optional:s},t?s?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:r}}(e.pathname),n=Object.keys(r.groups);for(let r of n)t[r]=e.query[r];return t}(ej),children:(0,E.jsx)(K.Provider,{value:ej,children:(0,E.jsx)(B.Provider,{value:eE,children:(0,E.jsx)(U.Provider,{value:{updateHead:e=>{eA=e},updateScripts:e=>{eR=e},scripts:eN,mountedInstances:new Set},children:(0,E.jsx)(W.Provider,{value:e=>eL.push(e),children:(0,E.jsx)(I.StyleRegistry,{registry:eT,children:(0,E.jsx)(eM.Provider,{value:q,children:e})})})})})})})})})})},eI=()=>null,eq=({children:e})=>(0,E.jsxs)(E.Fragment,{children:[(0,E.jsx)(eI,{}),(0,E.jsx)(eO,{children:(0,E.jsxs)(E.Fragment,{children:[x?(0,E.jsxs)(E.Fragment,{children:[e,(0,E.jsx)(eI,{})]}):e,(0,E.jsx)(eI,{})]})})]}),ez={err:y,req:ev?void 0:n,res:ev?void 0:s,pathname:i,query:a,asPath:eP,locale:l.locale,locales:l.locales,defaultLocale:l.defaultLocale,AppTree:e=>(0,E.jsx)(eq,{children:e2(H,V,{...e,router:ej})}),defaultGetInitialProps:async(e,t={})=>{let{html:r,head:n}=await e.renderPage({enhanceApp:e=>t=>(0,E.jsx)(e,{...t})}),o=eT.styles({nonce:t.nonce});return eT.flush(),{html:r,head:n,styles:o}}},eU=!Y&&(l.nextExport||x&&(ev||Q)),eW=()=>{let e=eT.styles();return eT.flush(),(0,E.jsx)(E.Fragment,{children:e})};if(m=await ei(H,{AppTree:ez.AppTree,Component:V,router:ej,ctx:ez}),(Y||R)&&g&&(m.__N_PREVIEW=!0),Y&&(m[M.NO]=!0),Y&&!Q){let e,t;try{e=await (0,em.getTracer)().trace(c.getStaticProps,{spanName:`getStaticProps ${i}`,attributes:{"next.route":i}},()=>j({...eh?{params:a}:void 0,...g?{draftMode:!0,preview:!0,previewData:f}:void 0,locales:l.locales,locale:l.locale,defaultLocale:l.defaultLocale}))}catch(e){throw e&&"ENOENT"===e.code&&delete e.code,e}if(null==e)throw Error(k.q6);let r=Object.keys(e).filter(e=>"revalidate"!==e&&"props"!==e&&"redirect"!==e&&"notFound"!==e);if(r.includes("unstable_revalidate"))throw Error(k.Eo);if(r.length)throw Error(e8("getStaticProps",r));if("notFound"in e&&e.notFound){if("/404"===i)throw Error('The /404 page can not return notFound in "getStaticProps", please remove it to continue!');v.isNotFound=!0}if("redirect"in e&&e.redirect&&"object"==typeof e.redirect){if(e9(e.redirect,n,"getStaticProps"),er)throw Error(`\`redirect\` can not be returned from getStaticProps during prerendering (${n.url})
See more info here: https://nextjs.org/docs/messages/gsp-redirect-during-prerender`);e.props={__N_REDIRECT:e.redirect.destination,__N_REDIRECT_STATUS:ef(e.redirect)},void 0!==e.redirect.basePath&&(e.props.__N_REDIRECT_BASE_PATH=e.redirect.basePath),v.isRedirect=!0}if((x||er)&&!v.isNotFound&&!z(i,"getStaticProps",e.props))throw Error("invariant: getStaticProps did not return valid props. Please report this.");if("revalidate"in e){if(e.revalidate&&"export"===l.nextConfigOutput)throw Error('ISR cannot be used with "output: export". See more info here: https://nextjs.org/docs/advanced-features/static-html-export');if("number"==typeof e.revalidate){if(Number.isInteger(e.revalidate)){if(e.revalidate<=0)throw Error(`A page's revalidate option can not be less than or equal to zero for ${n.url}. A revalidate option of zero means to revalidate after _every_ request, and implies stale data cannot be tolerated.

To never revalidate, you can set revalidate to \`false\` (only ran once at build-time).
To revalidate as soon as possible, you can set the value to \`1\`.`);e.revalidate>31536e3&&console.warn(`Warning: A page's revalidate option was set to more than a year for ${n.url}. This may have been done in error.
To only run getStaticProps at build-time and not revalidate at runtime, you can set \`revalidate\` to \`false\`!`),t=e.revalidate}else throw Error(`A page's revalidate option must be seconds expressed as a natural number for ${n.url}. Mixed numbers, such as '${e.revalidate}', cannot be used.
Try changing the value to '${Math.ceil(e.revalidate)}' or using \`Math.ceil()\` if you're computing the value.`)}else if(!0===e.revalidate)t=1;else if(!1===e.revalidate||void 0===e.revalidate)t=!1;else throw Error(`A page's revalidate option must be seconds expressed as a natural number. Mixed numbers and strings cannot be used. Received '${JSON.stringify(e.revalidate)}' for ${n.url}`)}else t=!1;if(m.pageProps=Object.assign({},m.pageProps,"props"in e?e.props:void 0),v.revalidate=t,v.pageData=m,v.isNotFound)return new ek(null,{metadata:v})}if(R&&(m[M.uY]=!0),R&&!Q){let e;let t=!1;try{e=await (0,em.getTracer)().trace(c.getServerSideProps,{spanName:`getServerSideProps ${i}`,attributes:{"next.route":i}},async()=>R({req:n,res:s,query:a,resolvedUrl:l.resolvedUrl,...eh?{params:A}:void 0,...!1!==f?{draftMode:!0,preview:!0,previewData:f}:void 0,locales:l.locales,locale:l.locale,defaultLocale:l.defaultLocale}))}catch(e){throw"object"==typeof e&&null!==e&&"name"in e&&"message"in e&&"ENOENT"===e.code&&delete e.code,e}if(null==e)throw Error(k.Lx);e.props instanceof Promise&&(t=!0);let r=Object.keys(e).filter(e=>"props"!==e&&"redirect"!==e&&"notFound"!==e);if(e.unstable_notFound)throw Error(`unstable_notFound has been renamed to notFound, please update the field to continue. Page: ${i}`);if(e.unstable_redirect)throw Error(`unstable_redirect has been renamed to redirect, please update the field to continue. Page: ${i}`);if(r.length)throw Error(e8("getServerSideProps",r));if("notFound"in e&&e.notFound){if("/404"===i)throw Error('The /404 page can not return notFound in "getStaticProps", please remove it to continue!');return v.isNotFound=!0,new ek(null,{metadata:v})}if("redirect"in e&&"object"==typeof e.redirect&&(e9(e.redirect,n,"getServerSideProps"),e.props={__N_REDIRECT:e.redirect.destination,__N_REDIRECT_STATUS:ef(e.redirect)},void 0!==e.redirect.basePath&&(e.props.__N_REDIRECT_BASE_PATH=e.redirect.basePath),v.isRedirect=!0),t&&(e.props=await e.props),(x||er)&&!z(i,"getServerSideProps",e.props))throw Error("invariant: getServerSideProps did not return valid props. Please report this.");m.pageProps=Object.assign({},m.pageProps,e.props),v.pageData=m}if(T&&!Y||v.isRedirect)return new ek(JSON.stringify(m),{metadata:v});if(Q&&(m.pageProps={}),es(s)&&!Y)return new ek(null,{metadata:v});let eZ=S;if(ev&&eh){let e;let t=(e=(function(e){let t=/^\/index(\/|$)/.test(e)&&!en(e)?"/index"+e:"/"===e?"/index":ee(e);{let{posix:e}=r("path"),n=e.normalize(t);if(n!==t)throw new el("Requested and resolved page mismatch: "+t+" "+n)}return t})(i).replace(/\\/g,"/")).startsWith("/index/")&&!en(e)?e.slice(6):"/index"!==e?e:"/";t in eZ.pages&&(eZ={...eZ,pages:{...eZ.pages,[t]:[...eZ.pages[t],...eZ.lowPriorityFiles.filter(e=>e.includes("_buildManifest"))]},lowPriorityFiles:eZ.lowPriorityFiles.filter(e=>!e.includes("_buildManifest"))})}let eY=({children:e})=>e$?e:(0,E.jsx)("div",{id:"__next",children:e}),eX=async()=>{let e,t,r;async function n(e){let t=async(t={})=>{if(ez.err&&_){e&&e(H,V);let t=await e4((0,E.jsx)(eY,{children:(0,E.jsx)(_,{error:ez.err})}));return{html:t,head:eA}}if(x&&(m.router||m.Component))throw Error("'router' and 'Component' can not be returned in getInitialProps from _app.js https://nextjs.org/docs/messages/cant-override-next-props");let{App:r,Component:n}="function"==typeof t?{App:H,Component:t(V)}:{App:t.enhanceApp?t.enhanceApp(H):H,Component:t.enhanceComponent?t.enhanceComponent(V):V};if(e)return e(r,n).then(async e=>{await e.allReady;let t=await eb(e);return{html:t,head:eA}});let o=await e4((0,E.jsx)(eY,{children:(0,E.jsx)(eq,{children:e2(r,n,{...m,router:ej})})}));return{html:o,head:eA}},r={...ez,renderPage:t},n=await ei(G,r);if(es(s)&&!Y)return null;if(!n||"string"!=typeof n.html){let e=`"${eo(G)}.getInitialProps()" should resolve to an object with a "html" prop set with a valid html string`;throw Error(e)}return{docProps:n,documentCtx:r}}G[M.wU];let o=(e,t)=>{let r=e||H,n=t||V;return ez.err&&_?(0,E.jsx)(eY,{children:(0,E.jsx)(_,{error:ez.err})}):(0,E.jsx)(eY,{children:(0,E.jsx)(eq,{children:e2(r,n,{...m,router:ej})})})},i=async(e,t)=>{let r=o(e,t);return await function({ReactDOMServer:e,element:t,streamOptions:r}){return(0,em.getTracer)().trace(p.renderToReadableStream,async()=>e.renderToReadableStream(t,r))}({ReactDOMServer:O(),element:r})},a=(0,em.getTracer)().wrap(c.createBodyResult,(e,t)=>ew(e,{suffix:t,inlinedDataStream:void 0,isStaticGeneration:!0,getServerInsertedHTML:()=>e4(eW()),serverInsertedHTMLToHead:!1,validateRootLayout:void 0})),l=!!G.getInitialProps;if(l){if(null===(t=await n(i)))return null;let{docProps:r}=t;e=e=>a(ex(r.html+e))}else{let r=await i(H,V);e=e=>a(r,e),t={}}let{docProps:d}=t||{};return l?(r=d.styles,eA=d.head):(r=eT.styles(),eT.flush()),{bodyResult:e,documentElement:e=>(0,E.jsx)(G,{...e,...d}),head:eA,headTags:[],styles:r}};null==(u=(0,em.getTracer)().getRootSpanAttributes())||u.set("next.route",l.page);let eK=await (0,em.getTracer)().trace(c.renderDocument,{spanName:`render route (pages) ${l.page}`,attributes:{"next.route":l.page}},async()=>eX());if(!eK)return new ek(null,{metadata:v});let e1=new Set,e6=new Set;for(let e of eL){let t=P[e];t&&(e1.add(t.id),t.files.forEach(e=>{e6.add(e)}))}let e5=eE.hybrid,{assetPrefix:e7,buildId:te,customServer:tt,defaultLocale:tr,disableOptimizedLoading:tn,domainLocales:to,locale:ts,locales:ti,runtimeConfig:ta}=l,tl={__NEXT_DATA__:{props:m,page:i,query:a,buildId:te,assetPrefix:""===e7?void 0:e7,runtimeConfig:ta,nextExport:!0===eU||void 0,autoExport:!0===ev||void 0,isFallback:Q,isExperimentalCompile:F,dynamicIds:0===e1.size?void 0:Array.from(e1),err:l.err?function(e,t){if(e){let e;return e="server",e=r("next/dist/compiled/@next/react-dev-overlay/dist/middleware").getErrorSource(t)||"server",{name:t.name,source:e,message:eD()(t.message),stack:t.stack,digest:t.digest}}return{name:"Internal Server Error.",message:"500 - Internal Server Error.",statusCode:500}}(x,l.err):void 0,gsp:!!j||void 0,gssp:!!R||void 0,customServer:tt,gip:!!eu||void 0,appGip:!ea||void 0,locale:ts,locales:ti,defaultLocale:tr,domainLocales:to,isPreview:!0===g||void 0,notFoundSrcPage:Z&&x?Z:void 0},strictNextHead:l.strictNextHead,buildManifest:eZ,docComponentsRendered:{},dangerousAsPath:ej.asPath,canonicalBase:!l.ampPath&&ep(n,"didStripLocale")?`${l.canonicalBase||""}/${l.locale}`:l.canonicalBase,ampPath:b,inAmpMode:e$,isDevelopment:!!x,hybridAmp:e5,dynamicImports:Array.from(e6),assetPrefix:e7,unstable_runtimeJS:w.unstable_runtimeJS,unstable_JsPreload:w.unstable_JsPreload,assetQueryString:J,scriptLoader:eR,locale:ts,disableOptimizedLoading:tn,head:eK.head,headTags:eK.headTags,styles:eK.styles,crossOrigin:l.crossOrigin,optimizeCss:l.optimizeCss,optimizeFonts:l.optimizeFonts,nextConfigOutput:l.nextConfigOutput,nextScriptWorkers:l.nextScriptWorkers,runtime:D,largePageDataBytes:l.largePageDataBytes,nextFontManifest:l.nextFontManifest},td=(0,E.jsx)(B.Provider,{value:eE,children:(0,E.jsx)(ed.Provider,{value:tl,children:eK.documentElement(tl)})}),tu=await (0,em.getTracer)().trace(c.renderToString,async()=>e4(td)),[tc,tp]=tu.split("<next-js-internal-body-render-target></next-js-internal-body-render-target>",2),th="";tu.startsWith(e0)||(th+=e0),th+=tc,e$&&(th+="<!-- __NEXT_DATA__ -->");let tf=await eb(ey(ex(th),await eK.bodyResult(tp))),tm=await o(i,tf,l,{inAmpMode:e$,hybridAmp:e5});return new ek(tm,{metadata:v})}let e5=(e,t,r,n,o)=>e6(e,t,r,n,o,o),e7=L().createContext(null);function te(e){let t=(0,A.useContext)(e7);t&&t(e)}class tt extends T{constructor(e){super(e),this.components=e.components}render(e,t,r){return e6(e,t,r.page,r.query,r.renderOpts,{App:this.components.App,Document:this.components.Document})}}let tr={contexts:R},tn=tt})(),module.exports=n})();
//# sourceMappingURL=pages.runtime.prod.js.map