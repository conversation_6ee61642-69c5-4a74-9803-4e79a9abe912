{"name": "elkjs", "version": "0.8.2", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "description": "Automatic graph layout based on <PERSON><PERSON><PERSON>'s algorithm. Specialized for data flow diagrams and ports.", "main": "lib/main", "types": "lib/main", "files": ["lib"], "repository": {"type": "git", "url": "https://github.com/kieler/elkjs.git"}, "keywords": ["graph", "layout", "layered", "ports", "dataflow"], "license": "EPL-2.0", "scripts": {"test": "mocha test/mocha", "gradle": "./gradlew lib", "babel": "babel src/js/ -d build/js/", "browserifyApi": "browserify build/js/main-api.js -s ELK --outfile lib/elk-api.js", "browserifyBundle": "browserify build/js/main-node.js -s ELK --outfile lib/elk.bundled.js", "browserify": "mkdirp lib && npm run browserifyApi && npm run browserifyBundle", "cp": "cp build/js/elk-worker*.js lib/ && cp build/js/main-node.js lib/main.js && cp typings/*.d.ts lib/", "js": "npm run babel && npm run browserify && npm run cp", "build": "npm run gradle && npm run js", "clean": "./gradlew clean && rm -r lib", "setDevVersion": "npm version --no-git-tag-version \"$(node -p \"require('./package.json').version\")-dev-$(date '+%Y%m%d')\""}, "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-env": "^1.6.0", "browserify": "^14.4.0", "chai": "^4.0.2", "chai-as-promised": "^7.1.1", "mkdirp": "^0.5.0", "mocha": "^10.0.0", "web-worker": "^1.0.0"}, "publishConfig": {"access": "public", "tag": "next"}}