{"name": "katex", "version": "0.13.24", "description": "Fast math typesetting for the web.", "main": "dist/katex.js", "homepage": "https://katex.org", "repository": {"type": "git", "url": "https://github.com/KaTeX/KaTeX.git"}, "funding": ["https://opencollective.com/katex", "https://github.com/sponsors/katex"], "files": ["katex.js", "cli.js", "src/", "contrib/", "dist/"], "license": "MIT", "packageManager": "yarn@3.0.1", "devDependencies": {"@babel/core": "^7.10.4", "@babel/eslint-parser": "^7.15.0", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/plugin-syntax-flow": "^7.14.5", "@babel/plugin-transform-react-jsx": "^7.14.9", "@babel/plugin-transform-runtime": "^7.10.4", "@babel/preset-env": "^7.10.4", "@babel/preset-flow": "^7.10.4", "@babel/preset-react": "^7.14.5", "@babel/register": "^7.10.4", "@babel/runtime": "^7.10.4", "@rollup/plugin-alias": "^3.1.1", "@rollup/plugin-babel": "^5.0.4", "@semantic-release/changelog": "^6.0.0", "@semantic-release/git": "^10.0.0", "babel-jest": "^27.0.0", "babel-loader": "^8.0.5", "babel-plugin-istanbul": "^6.0.0", "babel-plugin-preval": "^5.0.0", "babel-plugin-version-inline": "^1.0.0", "benchmark": "^2.1.4", "browserslist": "^4.13.0", "browserstack-local": "^1.4.5", "caniuse-lite": "^1.0.30001102", "css-loader": "^6.0.0", "cssnano": "^5.0.0-rc.1", "eslint": "^8.0.0", "eslint-import-resolver-webpack": "^0.13.2", "eslint-plugin-actions": "^2.0.0", "eslint-plugin-flowtype": "^8.0.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-react": "^7.20.3", "flow-bin": "^0.135.0", "fs-extra": "^10.0.0", "got": "^11.8.0", "husky": "^4.2.5", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-report": "^3.0.0", "istanbul-reports": "^3.0.2", "jest": "^27.0.0", "jest-diff": "^27.0.0", "jest-matcher-utils": "^27.0.0", "jest-message-util": "^27.0.0", "jest-serializer-html": "^7.0.0", "js-yaml": "^4.0.0", "json-stable-stringify": "^1.0.1", "jspngopt": "^0.2.0", "less": "^4.0.0", "less-loader": "^10.0.0", "mini-css-extract-plugin": "^2.0.0", "mkdirp": "^1.0.4", "p-retry": "^4.6.1", "pako": "^2.0.0", "postcss": "^8.0.0", "postcss-less": "^5.0.0", "postcss-loader": "^6.0.0", "postcss-preset-env": "^6.7.0", "prettier": "^2.0.5", "query-string": "^7.0.0", "rimraf": "^3.0.2", "rollup": "^2.21.0", "selenium-webdriver": "^4.0.0-beta.4", "semantic-release": "^18.0.0", "sri-toolbox": "^0.2.0", "style-loader": "^3.0.0", "stylelint": "^14.0.0", "stylelint-config-standard": "^23.0.0", "terser-webpack-plugin": "^5.0.3", "webpack": "^5.51.1", "webpack-bundle-analyzer": "^4.0.0", "webpack-cli": "^4.8.0", "webpack-dev-server": "^4.0.0"}, "bin": "cli.js", "scripts": {"test": "yarn test:lint && yarn test:flow && yarn test:jest", "test:lint": "yarn test:lint:js && yarn test:lint:css", "test:lint:js": "eslint .", "test:lint:css": "stylelint src/katex.less static/main.css contrib/**/*.css website/static/**/*.css", "test:flow": "flow", "test:jest": "jest", "test:jest:watch": "jest --watch", "test:jest:update": "jest --updateSnapshot", "test:jest:coverage": "jest --coverage", "test:screenshots": "yarn test:screenshots:update --verify", "test:screenshots:update": "dockers/screenshotter/screenshotter.sh", "test:perf": "NODE_ENV=test node test/perf-test.js", "clean": "rm -rf dist/ node_modules/", "clean-install": "yarn clean && yarn", "start": "webpack serve --config webpack.dev.js", "analyze": "webpack --config webpack.analyze.js", "build": "rimraf dist/ && mkdirp dist && cp README.md dist && rollup -c --failAfterWarnings && webpack && node update-sri.js package dist/README.md", "build:fonts": "dockers/fonts/buildFonts.sh", "build:metrics": "dockers/fonts/buildMetrics.sh", "watch": "yarn build --watch", "postversion": "yarn dist && node update-sri.js package README.md contrib/*/README.md docs/*.md website/pages/index.html", "semantic-release": "semantic-release", "dist": "yarn build && yarn dist:zip", "dist:zip": "rimraf katex/ katex.tar.gz katex.zip && cp -R dist katex && tar czf katex.tar.gz katex && zip -rq katex.zip katex && rimraf katex/"}, "dependencies": {"commander": "^8.0.0"}, "husky": {"hooks": {"pre-commit": "yarn test:lint"}}, "jest": {"collectCoverageFrom": ["src/**/*.js", "contrib/**/*.js", "!src/unicodeSymbols.js", "!contrib/mhchem/**"], "setupFilesAfterEnv": ["<rootDir>/test/setup.js"], "snapshotSerializers": ["jest-serializer-html"], "testMatch": ["**/test/*-spec.js"], "testURL": "http://localhost/", "transform": {"^.+\\.js$": "babel-jest"}, "moduleNameMapper": {"^katex$": "<rootDir>/katex.js"}}}