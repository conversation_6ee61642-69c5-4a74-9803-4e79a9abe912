# HawaiiHub代码审查流程

## 🔍 Pull Request模板

### PR标题格式
```
类型(范围): 简短描述

示例：
feat(ai): 添加语音识别功能
fix(auth): 修复登录超时问题
docs(readme): 更新安装指南
```

### PR描述模板
```markdown
## 📝 变更描述
简要描述本次变更的内容和目的

## 🎯 变更类型
- [ ] 新功能 (feature)
- [ ] Bug修复 (bugfix)
- [ ] 文档更新 (docs)
- [ ] 样式调整 (style)
- [ ] 代码重构 (refactor)
- [ ] 性能优化 (perf)
- [ ] 测试相关 (test)
- [ ] 构建相关 (build)

## 🧪 测试情况
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 手动测试完成
- [ ] 浏览器兼容性测试

## 📸 截图/演示
（如果是UI变更，请提供截图或GIF）

## 🔗 相关链接
- 相关Issue: #123
- 设计稿: [链接]
- 文档: [链接]

## ✅ 检查清单
- [ ] 代码符合项目规范
- [ ] 已添加必要的注释
- [ ] 已更新相关文档
- [ ] 已考虑向后兼容性
- [ ] 已进行安全检查
```

## 👀 审查标准

### 代码质量检查
```bash
# 1. 自动化检查
npm run lint          # ESLint检查
npm run type-check    # TypeScript类型检查
npm run test          # 运行测试
npm run build         # 构建检查

# 2. 安全检查
npm audit             # 依赖安全检查
npm run security-scan # 代码安全扫描
```

### 审查要点
1. **功能正确性** - 代码是否实现了预期功能
2. **代码质量** - 是否遵循编码规范
3. **性能影响** - 是否有性能问题
4. **安全性** - 是否存在安全漏洞
5. **可维护性** - 代码是否易于理解和维护
6. **测试覆盖** - 是否有足够的测试