'use strict';

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return call && (typeof call === "object" || typeof call === "function") ? call : self; }

function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function, not " + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }

/*******************************************************************************
 * Copyright (c) 2021 Kiel University and others.
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0.
 * 
 * SPDX-License-Identifier: EPL-2.0
 *******************************************************************************/
var ELK = require('./elk-api.js').default;

var ELKNode = function (_ELK) {
  _inherits(ELKNode, _ELK);

  function ELKNode() {
    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};

    _classCallCheck(this, ELKNode);

    var optionsClone = Object.assign({}, options);

    var workerThreadsExist = false;
    try {
      require.resolve('web-worker');
      workerThreadsExist = true;
    } catch (e) {}

    // user requested a worker
    if (options.workerUrl) {
      if (workerThreadsExist) {
        var Worker = require('web-worker');
        optionsClone.workerFactory = function (url) {
          return new Worker(url);
        };
      } else {
        console.warn('Web worker requested but \'web-worker\' package not installed. \nConsider installing the package or pass your own \'workerFactory\' to ELK\'s constructor.\n... Falling back to non-web worker version.');
      }
    }

    // unless no other workerFactory is registered, use the fake worker
    if (!optionsClone.workerFactory) {
      var _require = require('./elk-worker.min.js'),
          _Worker = _require.Worker;

      optionsClone.workerFactory = function (url) {
        return new _Worker(url);
      };
    }

    return _possibleConstructorReturn(this, (ELKNode.__proto__ || Object.getPrototypeOf(ELKNode)).call(this, optionsClone));
  }

  return ELKNode;
}(ELK);

Object.defineProperty(module.exports, "__esModule", {
  value: true
});
module.exports = ELKNode;
ELKNode.default = ELKNode;